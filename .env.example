# Supabase Configuration
# Copy this file to .env and replace the placeholder values with your actual Supabase credentials
# Get these values from your Supabase project dashboard: https://app.supabase.com/

# Your Supabase project URL (found in Settings > API)
VITE_SUPABASE_URL=your_supabase_project_url

# Your Supabase anon/public key (found in Settings > API)
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase Project Configuration (for CLI and local development)
# Your Supabase project ID (found in Settings > General)
SUPABASE_PROJECT_ID=your_supabase_project_id

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Replace all placeholder values with your actual Supabase credentials
# 3. Never commit the .env file to version control
# 4. Make sure .env is listed in your .gitignore file
