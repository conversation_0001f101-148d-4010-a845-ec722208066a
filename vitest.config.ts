import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react-swc";
import path from "path";

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./src/test/setup.ts"],
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    "import.meta.env.VITE_SUPABASE_URL": '"https://oyekojzsteofnqqznuzq.supabase.co"',
    "import.meta.env.VITE_SUPABASE_ANON_KEY":
      '"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95ZWtvanpzdGVvZm5xcXpudXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0OTYwOTcsImV4cCI6MjA2ODA3MjA5N30.ahBfBMCyBYx9nqm4bEpfw9-_1LxLbWYHgPnxAwzF7xY"',
  },
});
