// Supabase Edge Function for CoinGecko API integration
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Declare Deno global for TypeScript
declare const Deno: {
  serve: (handler: (req: Request) => Promise<Response> | Response) => void;
};

interface CoinGeckoRequest {
  symbol?: string;
  days?: number;
  interval?: string;
}

interface CoinGeckoResponse {
  prices: number[][];
  market_caps: number[][];
  total_volumes: number[][];
}

// In-memory cache for API responses
const cache = new Map<string, { data: CoinGeckoResponse; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Rate limiting
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 50; // requests per minute
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute

const checkRateLimit = (clientId: string): boolean => {
  const now = Date.now();
  const clientData = rateLimitMap.get(clientId);

  if (!clientData || now > clientData.resetTime) {
    rateLimitMap.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (clientData.count >= RATE_LIMIT) {
    return false;
  }

  clientData.count++;
  return true;
};

const getCachedData = (cacheKey: string): CoinGeckoResponse | null => {
  const cached = cache.get(cacheKey);

  if (!cached) {
    return null;
  }

  const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;

  if (isExpired) {
    cache.delete(cacheKey);
    return null;
  }

  return cached.data;
};

const setCachedData = (cacheKey: string, data: CoinGeckoResponse): void => {
  cache.set(cacheKey, {
    data,
    timestamp: Date.now(),
  });
};

const fetchFromCoinGecko = async (
  symbol: string,
  days: number,
  interval: string
): Promise<CoinGeckoResponse> => {
  const url = `https://api.coingecko.com/api/v3/coins/${symbol}/market_chart?vs_currency=usd&days=${days}&interval=${interval}`;

  console.log(`Fetching data from CoinGecko: ${url}`);

  const response = await fetch(url, {
    headers: {
      Accept: "application/json",
      "User-Agent": "Signal-Sight-Portfolio-Tracker/1.0",
    },
  });

  if (!response.ok) {
    throw new Error(`CoinGecko API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  // Validate response structure
  if (!data.prices || !Array.isArray(data.prices)) {
    throw new Error("Invalid CoinGecko API response format");
  }

  return data;
};

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400",
      },
    });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  try {
    // Get client IP for rate limiting
    const clientIp =
      req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown";

    // Check rate limit
    if (!checkRateLimit(clientIp)) {
      return new Response(
        JSON.stringify({
          error: "Rate limit exceeded",
          message: "Too many requests. Please try again later.",
        }),
        {
          status: 429,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Retry-After": "60",
          },
        }
      );
    }

    // Parse request body
    const requestBody: CoinGeckoRequest = await req.json();

    // Validate and set defaults
    const symbol = requestBody.symbol || "bitcoin";
    const days = Math.min(Math.max(requestBody.days || 200, 1), 365); // Limit to 1-365 days
    const interval = requestBody.interval || "daily";

    // Validate symbol (basic validation)
    if (!/^[a-z0-9-]+$/.test(symbol)) {
      return new Response(
        JSON.stringify({
          error: "Invalid symbol",
          message: "Symbol must contain only lowercase letters, numbers, and hyphens",
        }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Create cache key
    const cacheKey = `${symbol}-${days}-${interval}`;

    // Check cache first
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      console.log(`Returning cached data for ${cacheKey}`);
      return new Response(JSON.stringify(cachedData), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "X-Cache": "HIT",
        },
      });
    }

    // Fetch from CoinGecko API
    const data = await fetchFromCoinGecko(symbol, days, interval);

    // Cache the response
    setCachedData(cacheKey, data);

    console.log(`Successfully fetched and cached data for ${symbol}`);

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "X-Cache": "MISS",
      },
    });
  } catch (error) {
    console.error("Error in coingecko-data function:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";

    return new Response(
      JSON.stringify({
        error: "Internal server error",
        message: errorMessage,
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/coingecko-data' \
    --header 'Authorization: Bearer YOUR_SUPABASE_ANON_KEY' \
    --header 'Content-Type: application/json' \
    --data '{"symbol":"bitcoin","days":200,"interval":"daily"}'

*/
