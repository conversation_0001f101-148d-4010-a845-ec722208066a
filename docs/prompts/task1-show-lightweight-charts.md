# Chart Feature Architecture Refactoring - Implementation Plan

## Overview

Refactor the existing `src/pages/Index.tsx` component and create a new feature-based architecture for chart functionality with real CoinGecko API integration.

## Requirements Summary

### 1. **Create Feature Structure** under `src/components/features/charts/`:

```
src/components/features/charts/
├── _Chart.tsx          # Parent component (underscore prefix)
├── utils.ts            # Chart data utilities and generators
└── stores.ts           # Chart state management
```

### 2. **Main Chart Component Requirements**:

- Create `_Chart.tsx` as primary chart component using lightweight-charts library
- Support both stock and cryptocurrency data visualization (BTC, ETH, etc.)
- Include proper TypeScript types: `CandlestickData`, `HistogramData`, `UTCTimestamp`
- Implement responsive design that adapts to container size

### 3. **Data Management**:

- Move chart data generation logic to `utils.ts`
- Implement data fetching functions in `src/data/apis.ts`
- Create Supabase Edge Function for CoinGecko API integration
- CoinGecko API: `https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=200&interval=daily`
- Database: Use existing schema in `supabase/migrations/20250714130527-8fb34781-9d67-4a50-9c7e-6b0426e4df91.sql`

### 4. **Update Index Page**:

- Refactor `src/pages/Index.tsx` to use new chart feature components
- Remove existing chart data generation functions from Index.tsx
- Replace `AssetChart` with new `_Chart` component

### 5. **Maintain Existing Functionality**:

- Preserve all current chart features (candlestick display, MACD histogram, etc.)
- Ensure chart renders properly with mock data as fallback
- Keep same visual styling and behavior

### 6. **TypeScript Compliance**:

- Ensure all components pass `npx tsc --noEmit` without errors
- Use proper typing throughout, avoiding `any` types
- Follow established TypeScript patterns

---

## Detailed Implementation Plan

### **Phase 1: Foundation Setup**

#### Task 1: Create Feature Directory Structure

**Estimated Time**: 20 minutes
**Complexity**: Low
**Dependencies**: None

**Deliverables**:

- Create `src/components/features/charts/` directory
- Create placeholder files: `_Chart.tsx`, `ChartModal.tsx`, `utils.ts`, `stores.ts`
- Add proper TypeScript interfaces and exports

**Acceptance Criteria**:

- Directory structure matches specification exactly
- All files have proper TypeScript setup with no compilation errors
- Basic exports are defined for each module

#### Task 2: Create Chart Data Utilities

**Estimated Time**: 30 minutes
**Complexity**: Low
**Dependencies**: Task 1

**Deliverables**:

- Move `generateFakeData()` and `generateFakeMACDData()` from `Index.tsx` to `utils.ts`
- Enhance functions with proper TypeScript interfaces
- Add data validation and error handling
- Create utility functions for data transformation

**Acceptance Criteria**:

- All existing data generation logic preserved and enhanced
- Proper TypeScript types for all functions and return values
- Functions are pure and testable
- No breaking changes to existing data format

#### Task 3: Create Chart State Management

**Estimated Time**: 40 minutes
**Complexity**: Medium
**Dependencies**: Task 1, Task 2

**Deliverables**:

- Implement `stores.ts` with React hooks for chart state
- Create interfaces for chart configuration and data state
- Add loading, error, and data states management
- Implement chart options and settings persistence

**Acceptance Criteria**:

- Clean separation of state logic from UI components
- Proper TypeScript interfaces for all state objects
- Hooks follow React best practices
- State updates are optimized to prevent unnecessary re-renders

### **Phase 2: Core Components**

#### Task 4: Create Main Chart Component (\_Chart.tsx)

**Estimated Time**: 60 minutes
**Complexity**: High
**Dependencies**: Task 1, Task 2, Task 3

**Deliverables**:

- Create `_Chart.tsx` as main chart container component
- Migrate and enhance functionality from existing `AssetChart.tsx`
- Implement responsive design with proper resize handling
- Add proper TypeScript types for all props and internal state
- Integrate with chart state management from `stores.ts`

**Acceptance Criteria**:

- Component renders identical to current `AssetChart` functionality
- Proper TypeScript compliance with no `any` types
- Responsive design works on all screen sizes
- Component is properly memoized for performance
- All lightweight-charts features are preserved

#### Task 5: Create Chart Modal Component

**Estimated Time**: 40 minutes
**Complexity**: Medium
**Dependencies**: Task 4

**Deliverables**:

- Implement `ChartModal.tsx` for expanded chart view
- Add modal functionality with proper accessibility
- Implement larger chart display with enhanced controls
- Add keyboard navigation and focus management

**Acceptance Criteria**:

- Modal opens/closes smoothly with proper animations
- Chart scales properly in modal view
- Accessibility standards are met (ARIA labels, keyboard nav)
- Modal can be closed via ESC key or click outside
- Component integrates seamlessly with main chart

### **Phase 3: Data Integration**

#### Task 6, 7: Create Supabase Edge Function for CoinGecko (Data layer)

**Estimated Time**: 50 minutes
**Complexity**: High
**Dependencies**: Task 6

**Deliverables**:

- Create new Supabase Edge Function: `supabase/functions/coingecko-data/`
- Implement CoinGecko API integration with proper error handling
- Add response caching (5-minute cache) to respect rate limits
- Implement data transformation to match chart format
- Add proper CORS headers and authentication

**Acceptance Criteria**:

- Function successfully fetches 200-day BTC data from CoinGecko
- Proper error handling for API failures and rate limits
- Response is cached appropriately to minimize API calls
- Data format matches existing chart data structure
- Function can be deployed via `supabase functions deploy`

#### Task 8: Update Database Schema (if needed)

**Estimated Time**: 25 minutes
**Complexity**: Medium
**Dependencies**: Task 7

**Deliverables**:

- Review existing `price_data_daily` table schema
- Create migration if enhancements are needed for real-time data
- Add indexes for optimal query performance
- Update TypeScript types to match schema changes

**Acceptance Criteria**:

- Database schema supports efficient storage and retrieval of price data
- Proper indexes are in place for performance
- TypeScript types are updated to match any schema changes
- Migration runs successfully without data loss

### **Phase 4: Integration**

#### Task 9: Refactor Index.tsx

**Estimated Time**: 25 minutes
**Complexity**: Low
**Dependencies**: Task 4, Task 5

**Deliverables**:

- Remove chart data generation functions from `Index.tsx`
- Replace `AssetChart` import with new `_Chart` component
- Update component usage to match new API
- Ensure all existing functionality is preserved

**Acceptance Criteria**:

- Index page renders identically to current implementation
- No chart-related logic remains in Index.tsx
- New chart component integrates seamlessly
- Page performance is maintained or improved

#### Task 10: Update Other Pages (Dashboard.tsx, AssetDetail.tsx)

**Estimated Time**: 35 minutes
**Complexity**: Medium
**Dependencies**: Task 4, Task 9

**Deliverables**:

- Update `Dashboard.tsx` to use new chart components
- Update `AssetDetail.tsx` to use new chart components
- Ensure all existing chart functionality is preserved
- Test responsive behavior on all pages

**Acceptance Criteria**:

- All pages render charts identically to current implementation
- No breaking changes to existing page functionality
- Chart components work consistently across all pages
- Mobile responsiveness is maintained

### **Phase 5: Quality Assurance**

#### Task 11: TypeScript Compliance & Testing

**Estimated Time**: 40 minutes
**Complexity**: Medium
**Dependencies**: All previous tasks

**Deliverables**:

- Run `npx tsc --noEmit` and fix all TypeScript errors
- Run `npm run lint` and fix all linting issues
- Verify no `any` types are used in new code
- Test all chart functionality across different browsers
- Verify responsive design on mobile devices

**Acceptance Criteria**:

- Zero TypeScript compilation errors
- Zero linting errors
- No `any` types in new code
- All chart features work correctly
- Mobile responsiveness verified

#### Task 12: Deploy and Integration Testing

**Estimated Time**: 45 minutes
**Complexity**: High
**Dependencies**: Task 11, Task 7

**Deliverables**:

- Deploy Supabase Edge Function: `supabase functions deploy coingecko-data`
- Test real CoinGecko API integration end-to-end
- Verify fallback to mock data when API is unavailable
- Performance testing with real data
- Cross-browser compatibility testing

**Acceptance Criteria**:

- Supabase function deploys successfully
- Real BTC data loads and displays correctly in charts
- Graceful fallback to mock data when API fails
- Chart performance is acceptable with real data
- All functionality works across major browsers

---

## Risk Mitigation

### **Technical Risks**:

1. **CoinGecko API Rate Limits**: Implement caching and fallback mechanisms
2. **Chart Performance**: Use React.memo and data memoization for large datasets
3. **TypeScript Compliance**: Maintain current relaxed config while avoiding `any` types
4. **Mobile Compatibility**: Test touch interactions and responsive behavior

### **Implementation Risks**:

1. **Breaking Changes**: Maintain backward compatibility throughout refactoring
2. **State Management**: Ensure proper state isolation between chart instances
3. **Error Handling**: Comprehensive error boundaries and fallback states
4. **Deployment**: Test Supabase function deployment in staging environment

---

## Success Metrics

### **Functional Requirements**:

- ✅ All existing chart functionality preserved
- ✅ Real CoinGecko data integration working
- ✅ Responsive design on all devices
- ✅ TypeScript compliance with no `any` types

### **Performance Requirements**:

- ✅ Chart rendering time < 500ms
- ✅ API response time < 2s with caching
- ✅ Mobile touch interactions responsive
- ✅ Memory usage optimized for large datasets

### **Code Quality Requirements**:

- ✅ Zero TypeScript compilation errors
- ✅ Zero linting errors
- ✅ Proper component separation and reusability
- ✅ Comprehensive error handling

---

## Next Steps

1. **Review and Approve**: Review this implementation plan and provide feedback
2. **Begin Implementation**: Start with Phase 1 tasks in parallel where possible
3. **Iterative Testing**: Test each phase before proceeding to the next
4. **Final Integration**: Complete end-to-end testing with real data
5. **Documentation**: Update component documentation and usage examples

**Estimated Total Time**: 6-8 hours across 12 tasks
**Recommended Approach**: Implement in phases with testing between each phase

---

## Task Dependencies Visualization

```
Phase 1: Foundation
├── Task 1: Directory Structure (No dependencies)
├── Task 2: Data Utilities (→ Task 1)
└── Task 3: State Management (→ Task 1, Task 2)

Phase 2: Components
├── Task 4: Main Chart Component (→ Task 1, Task 2, Task 3)
└── Task 5: Chart Modal (→ Task 4)

Phase 3: Data Integration
├── Task 6: API Layer (→ Task 1)
├── Task 7: Supabase Function (→ Task 6)
└── Task 8: Database Schema (→ Task 7)

Phase 4: Integration
├── Task 9: Refactor Index.tsx (→ Task 4, Task 5)
└── Task 10: Update Other Pages (→ Task 4, Task 9)

Phase 5: Quality Assurance
├── Task 11: TypeScript & Testing (→ All previous tasks)
└── Task 12: Deploy & Integration Testing (→ Task 11, Task 7)
```

This plan provides a comprehensive roadmap for refactoring the chart functionality while maintaining all existing features and adding real CoinGecko API integration.
