**Languages and Technologies:**

- **Primary Language**: TypeScript/JavaScript (React)
- **Build Tool**: Vite
- **Package Manager**: npm (package.json and package-lock.json present)
- **Framework**: React with TypeScript
- **UI Library**: shadcn-ui with Tailwind CSS
- **Backend**:
  - Supabase integration
  - Supabase edge function:
    - supabase login, supabase create function is set up done

**Key Configuration Files:**

- `package.json` - npm package configuration
- `vite.config.ts` - Vite build configuration
- `tsconfig.json`, `tsconfig.app.json`, `tsconfig.node.json` - TypeScript configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `eslint.config.js` - ESLint configuration

**Testing Setup:**

- No vitest, jest, or other testing framework configuration files
