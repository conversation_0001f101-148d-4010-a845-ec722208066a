import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Chart } from "@/components/features/charts";

const Index = () => {
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-foreground">
              Signal - Portfolio Tracker
            </h1>
            <p className="text-xl text-muted-foreground">
              Technical analysis for your investments
            </p>
          </div>
          <Link to="/dashboard">
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              View Dashboard
            </Button>
          </Link>
        </div>

        <div className="bg-card rounded-lg border p-6">
          <h2 className="text-2xl font-semibold mb-4 text-card-foreground">
            BTC/USD Chart with MACD
          </h2>
          <Chart
            options={{
              symbol: "bitcoin",
              dataSource: "api",
              height: 400,
              showMACD: true,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default Index;
