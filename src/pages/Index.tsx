import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Chart } from "@/components/features/charts";

const Index = () => {
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-foreground">
              Signal - Portfolio Tracker
            </h1>
            <p className="text-xl text-muted-foreground">
              Technical analysis for your investments
            </p>
          </div>
          <Link to="/dashboard">
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              View Dashboard
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>BTC/USD Chart with MACD</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[400px]">
              <Chart
                options={{
                  symbol: "bitcoin",
                  dataSource: "api",
                  height: 400,
                  showMACD: true,
                }}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
