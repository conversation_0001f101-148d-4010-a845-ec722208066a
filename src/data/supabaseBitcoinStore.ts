import { createClient } from '@supabase/supabase-js';
import { CandlestickData, MACDData } from '@/components/features/charts/utils';
import { getChartData } from './apis';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://oyekojzsteofnqqznuzq.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95ZWtvanpzdGVvZm5xcXpudXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0OTYwOTcsImV4cCI6MjA2ODA3MjA5N30.ahBfBMCyBYx9nqm4bEpfw9-_1LxLbWYHgPnxAwzF7xY';

const supabase = createClient(supabaseUrl, supabaseKey);

export interface SupabaseBitcoinData {
  id?: number;
  symbol: string;
  time_unix: number;
  open: number;
  high: number;
  low: number;
  close: number;
  created_at?: string;
  updated_at?: string;
}

export interface SupabaseMACDData {
  id?: number;
  symbol: string;
  time_unix: number;
  macd_value: number;
  created_at?: string;
  updated_at?: string;
}

export type TimeInterval = "12h" | "1D" | "2D" | "3D" | "1W";

class SupabaseBitcoinDataManager {
  private static instance: SupabaseBitcoinDataManager;
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  private constructor() {}

  static getInstance(): SupabaseBitcoinDataManager {
    if (!SupabaseBitcoinDataManager.instance) {
      SupabaseBitcoinDataManager.instance = new SupabaseBitcoinDataManager();
    }
    return SupabaseBitcoinDataManager.instance;
  }

  /**
   * Create tables if they don't exist
   */
  async initializeTables(): Promise<void> {
    console.log('🏗️ [SUPABASE] Initializing Bitcoin data tables...');

    // Bitcoin price data table
    const { error: priceTableError } = await supabase.rpc('create_bitcoin_price_table');
    if (priceTableError && !priceTableError.message.includes('already exists')) {
      console.error('❌ [SUPABASE] Error creating price table:', priceTableError);
    }

    // MACD data table
    const { error: macdTableError } = await supabase.rpc('create_bitcoin_macd_table');
    if (macdTableError && !macdTableError.message.includes('already exists')) {
      console.error('❌ [SUPABASE] Error creating MACD table:', macdTableError);
    }

    console.log('✅ [SUPABASE] Tables initialized');
  }

  /**
   * Store comprehensive Bitcoin data in Supabase
   */
  async storeComprehensiveData(): Promise<void> {
    console.log('📡 [SUPABASE] Fetching and storing comprehensive Bitcoin data...');

    try {
      // Fetch fresh data from API
      const apiResult = await getChartData('bitcoin', 200, false);
      
      // Convert to Supabase format
      const priceData: SupabaseBitcoinData[] = apiResult.data.map(point => ({
        symbol: 'BITCOIN',
        time_unix: point.time as number,
        open: point.open,
        high: point.high,
        low: point.low,
        close: point.close
      }));

      const macdData: SupabaseMACDData[] = apiResult.macdData.map(point => ({
        symbol: 'BITCOIN',
        time_unix: point.time as number,
        macd_value: point.value
      }));

      // Clear existing data for Bitcoin
      await supabase
        .from('bitcoin_price_data')
        .delete()
        .eq('symbol', 'BITCOIN');

      await supabase
        .from('bitcoin_macd_data')
        .delete()
        .eq('symbol', 'BITCOIN');

      // Insert new price data
      const { error: priceError } = await supabase
        .from('bitcoin_price_data')
        .insert(priceData);

      if (priceError) {
        throw new Error(`Price data insert failed: ${priceError.message}`);
      }

      // Insert new MACD data
      const { error: macdError } = await supabase
        .from('bitcoin_macd_data')
        .insert(macdData);

      if (macdError) {
        throw new Error(`MACD data insert failed: ${macdError.message}`);
      }

      console.log('✅ [SUPABASE] Comprehensive data stored:', {
        pricePoints: priceData.length,
        macdPoints: macdData.length
      });

    } catch (error) {
      console.error('❌ [SUPABASE] Failed to store comprehensive data:', error);
      throw error;
    }
  }

  /**
   * Get Bitcoin data for specific time interval from Supabase
   */
  async getBitcoinDataForInterval(interval: TimeInterval): Promise<{ data: CandlestickData[], macdData: MACDData[] }> {
    console.log(`🔍 [SUPABASE] Getting Bitcoin data for interval: ${interval}`);

    try {
      const now = Math.floor(Date.now() / 1000);
      let cutoffTime: number;

      // Calculate cutoff time based on interval
      switch (interval) {
        case '12h':
          cutoffTime = now - (12 * 60 * 60);
          break;
        case '1D':
          cutoffTime = now - (24 * 60 * 60);
          break;
        case '2D':
          cutoffTime = now - (2 * 24 * 60 * 60);
          break;
        case '3D':
          cutoffTime = now - (3 * 24 * 60 * 60);
          break;
        case '1W':
          cutoffTime = now - (7 * 24 * 60 * 60);
          break;
        default:
          cutoffTime = now - (24 * 60 * 60);
      }

      // Fetch price data
      const { data: priceData, error: priceError } = await supabase
        .from('bitcoin_price_data')
        .select('*')
        .eq('symbol', 'BITCOIN')
        .gte('time_unix', cutoffTime)
        .order('time_unix', { ascending: true });

      if (priceError) {
        throw new Error(`Price data fetch failed: ${priceError.message}`);
      }

      // Fetch MACD data
      const { data: macdData, error: macdError } = await supabase
        .from('bitcoin_macd_data')
        .select('*')
        .eq('symbol', 'BITCOIN')
        .gte('time_unix', cutoffTime)
        .order('time_unix', { ascending: true });

      if (macdError) {
        throw new Error(`MACD data fetch failed: ${macdError.message}`);
      }

      // Convert back to chart format
      const chartData: CandlestickData[] = (priceData || []).map(point => ({
        time: point.time_unix,
        open: point.open,
        high: point.high,
        low: point.low,
        close: point.close
      }));

      const chartMacdData: MACDData[] = (macdData || []).map(point => ({
        time: point.time_unix,
        value: point.macd_value
      }));

      console.log(`✅ [SUPABASE] Data retrieved for ${interval}:`, {
        pricePoints: chartData.length,
        macdPoints: chartMacdData.length,
        timeRange: chartData.length > 0 ? {
          from: new Date(chartData[0].time * 1000).toISOString(),
          to: new Date(chartData[chartData.length - 1].time * 1000).toISOString()
        } : 'No data'
      });

      return {
        data: chartData,
        macdData: chartMacdData
      };

    } catch (error) {
      console.error(`❌ [SUPABASE] Failed to get data for ${interval}:`, error);
      throw error;
    }
  }

  /**
   * Check if data needs refresh (older than cache duration)
   */
  async needsDataRefresh(): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('bitcoin_price_data')
        .select('created_at')
        .eq('symbol', 'BITCOIN')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error || !data || data.length === 0) {
        return true; // No data exists, needs refresh
      }

      const lastUpdate = new Date(data[0].created_at).getTime();
      const now = Date.now();
      const needsRefresh = (now - lastUpdate) > this.CACHE_DURATION;

      console.log('🕐 [SUPABASE] Data freshness check:', {
        lastUpdate: new Date(lastUpdate).toISOString(),
        ageMinutes: Math.round((now - lastUpdate) / (60 * 1000)),
        needsRefresh
      });

      return needsRefresh;
    } catch (error) {
      console.error('❌ [SUPABASE] Error checking data freshness:', error);
      return true; // On error, assume refresh needed
    }
  }

  /**
   * Get data statistics
   */
  async getDataStats(): Promise<{ pricePoints: number, macdPoints: number, lastUpdated: string | null }> {
    try {
      const { count: priceCount } = await supabase
        .from('bitcoin_price_data')
        .select('*', { count: 'exact', head: true })
        .eq('symbol', 'BITCOIN');

      const { count: macdCount } = await supabase
        .from('bitcoin_macd_data')
        .select('*', { count: 'exact', head: true })
        .eq('symbol', 'BITCOIN');

      const { data: latestData } = await supabase
        .from('bitcoin_price_data')
        .select('created_at')
        .eq('symbol', 'BITCOIN')
        .order('created_at', { ascending: false })
        .limit(1);

      return {
        pricePoints: priceCount || 0,
        macdPoints: macdCount || 0,
        lastUpdated: latestData?.[0]?.created_at || null
      };
    } catch (error) {
      console.error('❌ [SUPABASE] Error getting data stats:', error);
      return { pricePoints: 0, macdPoints: 0, lastUpdated: null };
    }
  }
}

// Export singleton instance
export const supabaseBitcoinDataManager = SupabaseBitcoinDataManager.getInstance();

// Convenience functions
export async function getSupabaseBitcoinDataForInterval(interval: TimeInterval): Promise<{ data: CandlestickData[], macdData: MACDData[] }> {
  console.log(`🎯 [SUPABASE API] Getting Bitcoin data for interval: ${interval}`);
  
  // Check if data needs refresh
  const needsRefresh = await supabaseBitcoinDataManager.needsDataRefresh();
  if (needsRefresh) {
    console.log('🔄 [SUPABASE API] Data needs refresh, updating...');
    await supabaseBitcoinDataManager.storeComprehensiveData();
  }
  
  // Get filtered data for interval
  return await supabaseBitcoinDataManager.getBitcoinDataForInterval(interval);
}

export async function refreshSupabaseBitcoinData(): Promise<void> {
  console.log('🔄 [SUPABASE API] Force refreshing Bitcoin data...');
  await supabaseBitcoinDataManager.storeComprehensiveData();
}

export async function getSupabaseBitcoinStats() {
  return await supabaseBitcoinDataManager.getDataStats();
}
