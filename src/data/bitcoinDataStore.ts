import { getChartData } from "./apis";

export interface BitcoinDataStore {
  data: CandlestickData[];
  macdData: MACDData[];
  lastUpdated: string;
  symbol: string;
  source: string;
}

export type TimeInterval = "12h" | "1D" | "2D" | "3D" | "1W";

class BitcoinDataManager {
  private static instance: BitcoinDataManager;
  private dataStore: BitcoinDataStore | null = null;
  private readonly STORAGE_KEY = "bitcoin_data_store";
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds

  private constructor() {}

  static getInstance(): BitcoinDataManager {
    if (!BitcoinDataManager.instance) {
      BitcoinDataManager.instance = new BitcoinDataManager();
    }
    return BitcoinDataManager.instance;
  }

  /**
   * Get comprehensive Bitcoin data (200 days) from storage or API
   */
  async getComprehensiveData(): Promise<BitcoinDataStore> {
    console.log("🏪 [BITCOIN STORE] Getting comprehensive data...");

    // Try to load from localStorage first
    const cachedData = this.loadFromStorage();
    if (cachedData && this.isCacheValid(cachedData)) {
      console.log("✅ [BITCOIN STORE] Using cached data:", {
        dataPoints: cachedData.data.length,
        macdPoints: cachedData.macdData.length,
        lastUpdated: cachedData.lastUpdated,
      });
      this.dataStore = cachedData;
      return cachedData;
    }

    // Fetch fresh data from API
    console.log("📡 [BITCOIN STORE] Fetching fresh data from API...");
    try {
      const result = await getChartData("bitcoin", 200, false);

      const newDataStore: BitcoinDataStore = {
        data: result.data,
        macdData: result.macdData,
        lastUpdated: new Date().toISOString(),
        symbol: result.symbol,
        source: result.source,
      };

      console.log("✅ [BITCOIN STORE] Fresh data fetched:", {
        dataPoints: newDataStore.data.length,
        macdPoints: newDataStore.macdData.length,
        source: newDataStore.source,
        symbol: newDataStore.symbol,
      });

      // Save to storage and memory
      this.saveToStorage(newDataStore);
      this.dataStore = newDataStore;

      return newDataStore;
    } catch (error) {
      console.error("❌ [BITCOIN STORE] Failed to fetch fresh data:", error);

      // Return cached data even if expired as fallback
      if (cachedData) {
        console.log("⚠️ [BITCOIN STORE] Using expired cached data as fallback");
        this.dataStore = cachedData;
        return cachedData;
      }

      throw error;
    }
  }

  /**
   * Filter data for specific time interval
   */
  filterDataForInterval(interval: TimeInterval): {
    data: CandlestickData[];
    macdData: MACDData[];
  } {
    if (!this.dataStore) {
      throw new Error("No data store available. Call getComprehensiveData() first.");
    }

    console.log(`🔍 [BITCOIN STORE] Filtering data for interval: ${interval}`);

    const now = Date.now() / 1000; // Current time in seconds
    let cutoffTime: number;

    // Calculate cutoff time based on interval
    switch (interval) {
      case "12h":
        cutoffTime = now - 12 * 60 * 60; // 12 hours ago
        break;
      case "1D":
        cutoffTime = now - 24 * 60 * 60; // 1 day ago
        break;
      case "2D":
        cutoffTime = now - 2 * 24 * 60 * 60; // 2 days ago
        break;
      case "3D":
        cutoffTime = now - 3 * 24 * 60 * 60; // 3 days ago
        break;
      case "1W":
        cutoffTime = now - 7 * 24 * 60 * 60; // 1 week ago
        break;
      default:
        cutoffTime = now - 24 * 60 * 60; // Default to 1 day
    }

    // Filter price data
    const filteredData = this.dataStore.data.filter((point) => {
      const pointTime = point.time as number;
      return pointTime >= cutoffTime;
    });

    // Filter MACD data
    const filteredMacdData = this.dataStore.macdData.filter((point) => {
      const pointTime = point.time as number;
      return pointTime >= cutoffTime;
    });

    console.log(`✅ [BITCOIN STORE] Filtered data for ${interval}:`, {
      originalDataPoints: this.dataStore.data.length,
      filteredDataPoints: filteredData.length,
      originalMacdPoints: this.dataStore.macdData.length,
      filteredMacdPoints: filteredMacdData.length,
      cutoffTime: new Date(cutoffTime * 1000).toISOString(),
      timeRange:
        filteredData.length > 0
          ? {
              from: new Date((filteredData[0].time as number) * 1000).toISOString(),
              to: new Date(
                (filteredData[filteredData.length - 1].time as number) * 1000
              ).toISOString(),
            }
          : "No data",
    });

    return {
      data: filteredData,
      macdData: filteredMacdData,
    };
  }

  /**
   * Force refresh data from API
   */
  async refreshData(): Promise<BitcoinDataStore> {
    console.log("🔄 [BITCOIN STORE] Force refreshing data...");

    // Clear cached data
    this.clearStorage();
    this.dataStore = null;

    // Fetch fresh data
    return await this.getComprehensiveData();
  }

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(data: BitcoinDataStore): boolean {
    const lastUpdated = new Date(data.lastUpdated).getTime();
    const now = Date.now();
    const isValid = now - lastUpdated < this.CACHE_DURATION;

    console.log(`🕐 [BITCOIN STORE] Cache validity check:`, {
      lastUpdated: data.lastUpdated,
      ageMinutes: Math.round((now - lastUpdated) / (60 * 1000)),
      maxAgeMinutes: this.CACHE_DURATION / (60 * 1000),
      isValid,
    });

    return isValid;
  }

  /**
   * Save data to localStorage
   */
  private saveToStorage(data: BitcoinDataStore): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
      console.log("💾 [BITCOIN STORE] Data saved to localStorage");
    } catch (error) {
      console.error("❌ [BITCOIN STORE] Failed to save to localStorage:", error);
    }
  }

  /**
   * Load data from localStorage
   */
  private loadFromStorage(): BitcoinDataStore | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored) as BitcoinDataStore;
        console.log("📂 [BITCOIN STORE] Data loaded from localStorage");
        return data;
      }
    } catch (error) {
      console.error("❌ [BITCOIN STORE] Failed to load from localStorage:", error);
    }
    return null;
  }

  /**
   * Clear stored data
   */
  private clearStorage(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log("🗑️ [BITCOIN STORE] Storage cleared");
    } catch (error) {
      console.error("❌ [BITCOIN STORE] Failed to clear storage:", error);
    }
  }

  /**
   * Get current data store info
   */
  getStoreInfo(): {
    hasData: boolean;
    dataPoints: number;
    macdPoints: number;
    lastUpdated: string | null;
  } {
    return {
      hasData: !!this.dataStore,
      dataPoints: this.dataStore?.data.length || 0,
      macdPoints: this.dataStore?.macdData.length || 0,
      lastUpdated: this.dataStore?.lastUpdated || null,
    };
  }
}

// Export singleton instance
export const bitcoinDataManager = BitcoinDataManager.getInstance();

// Convenience functions
export async function getBitcoinDataForInterval(
  interval: TimeInterval
): Promise<{ data: CandlestickData[]; macdData: MACDData[] }> {
  console.log(`🎯 [BITCOIN API] Getting Bitcoin data for interval: ${interval}`);

  // Ensure we have comprehensive data
  await bitcoinDataManager.getComprehensiveData();

  // Filter for the specific interval
  return bitcoinDataManager.filterDataForInterval(interval);
}

export async function refreshBitcoinData(): Promise<BitcoinDataStore> {
  return await bitcoinDataManager.refreshData();
}

export function getBitcoinStoreInfo() {
  return bitcoinDataManager.getStoreInfo();
}
