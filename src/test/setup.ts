import "@testing-library/jest-dom";
import { vi } from "vitest";

// Mock environment variables
Object.defineProperty(window, "location", {
  value: {
    href: "http://localhost:3000",
  },
  writable: true,
});

// Setup for Supabase environment variables
process.env.VITE_SUPABASE_URL = "https://oyekojzsteofnqqznuzq.supabase.co";
process.env.VITE_SUPABASE_ANON_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95ZWtvanpzdGVvZm5xcXpudXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0OTYwOTcsImV4cCI6MjA2ODA3MjA5N30.ahBfBMCyBYx9nqm4bEpfw9-_1LxLbWYHgPnxAwzF7xY";

// Make fetch available globally for Node.js environment
if (!globalThis.fetch) {
  const { fetch, Headers, Request, Response } = await import("undici");
  globalThis.fetch = fetch as any;
  globalThis.Headers = Headers as any;
  globalThis.Request = Request as any;
  globalThis.Response = Response as any;
}
