import { describe, it, expect, beforeEach } from 'vitest';
import { getChartData } from '@/data/apis';

describe('Final Integration Test - Real Bitcoin Data', () => {
  beforeEach(() => {
    // Clear any existing console logs
    console.clear();
  });

  it('should successfully fetch and display real Bitcoin data with current price around $118,250', async () => {
    console.log('🚀 Starting final integration test...');
    
    const result = await getChartData('bitcoin', 7, false);
    
    // Basic structure validation
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.macdData).toBeDefined();
    expect(result.symbol).toBe('BITCOIN');
    
    // Data should not be empty
    expect(result.data.length).toBeGreaterThan(0);
    expect(result.macdData.length).toBeGreaterThan(0);
    
    console.log('📊 Integration Test Results:');
    console.log(`   Data Source: ${result.source}`);
    console.log(`   Symbol: ${result.symbol}`);
    console.log(`   Data Points: ${result.data.length}`);
    console.log(`   MACD Points: ${result.macdData.length}`);
    console.log(`   Last Updated: ${result.lastUpdated}`);
    
    if (result.data.length > 0) {
      const latestPrice = result.data[result.data.length - 1];
      const currentPrice = latestPrice.close;
      
      console.log('💰 Current Bitcoin Price Data:');
      console.log(`   Time: ${new Date(latestPrice.time * 1000).toLocaleString()}`);
      console.log(`   Open: $${latestPrice.open.toLocaleString()}`);
      console.log(`   High: $${latestPrice.high.toLocaleString()}`);
      console.log(`   Low: $${latestPrice.low.toLocaleString()}`);
      console.log(`   Close: $${latestPrice.close.toLocaleString()}`);
      
      // Validate price is in expected range (around $118,250)
      const expectedMin = 110000; // Allow 10% variance
      const expectedMax = 130000;
      
      expect(currentPrice).toBeGreaterThan(expectedMin);
      expect(currentPrice).toBeLessThan(expectedMax);
      
      console.log(`✅ Price validation passed: $${currentPrice.toLocaleString()} is within expected range ($${expectedMin.toLocaleString()} - $${expectedMax.toLocaleString()})`);
      
      // Validate OHLC relationships
      expect(latestPrice.high).toBeGreaterThanOrEqual(latestPrice.open);
      expect(latestPrice.high).toBeGreaterThanOrEqual(latestPrice.close);
      expect(latestPrice.low).toBeLessThanOrEqual(latestPrice.open);
      expect(latestPrice.low).toBeLessThanOrEqual(latestPrice.close);
      
      console.log('✅ OHLC data validation passed');
    }
    
    // Validate MACD data structure
    if (result.macdData.length > 0) {
      const latestMACD = result.macdData[result.macdData.length - 1];
      expect(latestMACD).toHaveProperty('time');
      expect(latestMACD).toHaveProperty('value');
      expect(typeof latestMACD.time).toBe('number');
      expect(typeof latestMACD.value).toBe('number');
      
      console.log(`✅ MACD data validation passed (latest value: ${latestMACD.value.toFixed(4)})`);
    }
    
    // Check if we're getting real data (not fallback)
    if (result.source === 'api') {
      console.log('🎉 SUCCESS: Real Bitcoin data is being fetched from CoinGecko API!');
      console.log('🔗 Chart component should now display live Bitcoin prices');
    } else {
      console.log('⚠️  WARNING: Using fallback mock data - API might be failing');
    }
    
    console.log('✅ Final integration test completed successfully!');
    
    return result;
  });

  it('should validate data freshness (updated within last hour)', async () => {
    const result = await getChartData('bitcoin', 1, false);
    
    if (result.source === 'api' && result.data.length > 0) {
      const latestDataPoint = result.data[result.data.length - 1];
      const dataTime = new Date(latestDataPoint.time * 1000);
      const now = new Date();
      const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      // Data should be relatively fresh (within last hour for daily data)
      expect(dataTime.getTime()).toBeGreaterThan(hourAgo.getTime() - 24 * 60 * 60 * 1000); // Allow 24 hours for daily data
      
      console.log(`📅 Data freshness check passed: Latest data from ${dataTime.toLocaleString()}`);
    }
  });

  it('should handle multiple concurrent requests efficiently', async () => {
    const startTime = Date.now();
    
    // Make multiple concurrent requests
    const promises = [
      getChartData('bitcoin', 7, true),
      getChartData('bitcoin', 7, true),
      getChartData('bitcoin', 7, true)
    ];
    
    const results = await Promise.all(promises);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    // All results should be identical (cached)
    expect(results[0]).toEqual(results[1]);
    expect(results[1]).toEqual(results[2]);
    
    // Should be reasonably fast due to caching
    expect(totalTime).toBeLessThan(10000); // Less than 10 seconds
    
    console.log(`⚡ Concurrent requests test passed in ${totalTime}ms`);
  });
});
