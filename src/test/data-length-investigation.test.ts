import { describe, it, expect } from 'vitest';
import { getChartData } from '@/data/apis';
import { generateFakeData } from '@/components/features/charts/utils';

describe('Data Length Investigation', () => {
  it('should compare real API data vs mock data lengths', async () => {
    console.log('🔍 Investigating data length differences...');
    
    // Test real API data
    const realData = await getChartData('bitcoin', 200, false);
    console.log('📊 Real API Data:');
    console.log(`   Source: ${realData.source}`);
    console.log(`   Data points: ${realData.data.length}`);
    console.log(`   MACD points: ${realData.macdData.length}`);
    
    if (realData.data.length > 0) {
      const firstPoint = realData.data[0];
      const lastPoint = realData.data[realData.data.length - 1];
      console.log(`   Time range: ${new Date(firstPoint.time * 1000).toDateString()} to ${new Date(lastPoint.time * 1000).toDateString()}`);
      console.log(`   Price range: $${Math.min(...realData.data.map(d => d.low)).toLocaleString()} - $${Math.max(...realData.data.map(d => d.high)).toLocaleString()}`);
    }
    
    // Test mock data with same parameters
    const mockData = generateFakeData({
      dataPoints: 200,
      basePrice: 45000,
      startDate: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000),
    });
    
    console.log('🎭 Mock Data:');
    console.log(`   Data points: ${mockData.length}`);
    
    if (mockData.length > 0) {
      const firstPoint = mockData[0];
      const lastPoint = mockData[mockData.length - 1];
      console.log(`   Time range: ${new Date(firstPoint.time * 1000).toDateString()} to ${new Date(lastPoint.time * 1000).toDateString()}`);
      console.log(`   Price range: $${Math.min(...mockData.map(d => d.low)).toLocaleString()} - $${Math.max(...mockData.map(d => d.high)).toLocaleString()}`);
    }
    
    // Compare lengths
    console.log('📏 Length Comparison:');
    console.log(`   Real data: ${realData.data.length} points`);
    console.log(`   Mock data: ${mockData.length} points`);
    console.log(`   Difference: ${realData.data.length - mockData.length} points`);
    
    // Both should have 200+ data points
    expect(realData.data.length).toBeGreaterThan(190); // Allow some variance
    expect(mockData.length).toBe(200);
    
    // Check data density (time gaps)
    if (realData.data.length > 1) {
      const realTimeGaps = [];
      for (let i = 1; i < Math.min(realData.data.length, 10); i++) {
        const gap = realData.data[i].time - realData.data[i-1].time;
        realTimeGaps.push(gap);
      }
      const avgRealGap = realTimeGaps.reduce((a, b) => a + b, 0) / realTimeGaps.length;
      console.log(`   Real data avg time gap: ${avgRealGap / 3600} hours`);
    }
    
    if (mockData.length > 1) {
      const mockTimeGaps = [];
      for (let i = 1; i < Math.min(mockData.length, 10); i++) {
        const gap = mockData[i].time - mockData[i-1].time;
        mockTimeGaps.push(gap);
      }
      const avgMockGap = mockTimeGaps.reduce((a, b) => a + b, 0) / mockTimeGaps.length;
      console.log(`   Mock data avg time gap: ${avgMockGap / 3600} hours`);
    }
  });

  it('should test different day ranges for API data', async () => {
    console.log('📅 Testing different day ranges...');
    
    const testRanges = [7, 30, 90, 200];
    
    for (const days of testRanges) {
      const data = await getChartData('bitcoin', days, false);
      console.log(`   ${days} days: ${data.data.length} data points (${data.source})`);
      
      // Should get approximately the requested number of days
      if (data.source === 'api') {
        expect(data.data.length).toBeGreaterThan(days * 0.9); // Allow 10% variance
        expect(data.data.length).toBeLessThan(days * 1.2); // Allow 20% extra
      }
    }
  });

  it('should analyze the current chart configuration', async () => {
    console.log('⚙️ Analyzing current chart configuration...');
    
    // This simulates what the chart component is actually requesting
    const chartData = await getChartData('bitcoin', 200, true);
    
    console.log('📈 Chart Data Analysis:');
    console.log(`   Source: ${chartData.source}`);
    console.log(`   Symbol: ${chartData.symbol}`);
    console.log(`   Data points: ${chartData.data.length}`);
    console.log(`   MACD points: ${chartData.macdData.length}`);
    console.log(`   Last updated: ${chartData.lastUpdated}`);
    
    // Check if MACD data is being generated properly
    if (chartData.macdData.length === 0) {
      console.log('⚠️  MACD data is empty - this might affect chart appearance');
    }
    
    // Validate data structure
    if (chartData.data.length > 0) {
      const samplePoint = chartData.data[0];
      console.log('📊 Sample data point structure:', {
        time: new Date(samplePoint.time * 1000).toISOString(),
        open: samplePoint.open,
        high: samplePoint.high,
        low: samplePoint.low,
        close: samplePoint.close
      });
    }
    
    expect(chartData.data.length).toBeGreaterThan(0);
  });
});
