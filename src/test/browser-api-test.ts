// Simple test to verify API works in browser environment
import { getChartData } from '@/data/apis';

// This function can be called from browser console to test API
(window as any).testBitcoinAPI = async () => {
  console.log('🚀 Testing Bitcoin API...');
  
  try {
    const result = await getChartData('bitcoin', 7, false);
    
    console.log('✅ API Test Results:');
    console.log('📊 Data source:', result.source);
    console.log('📈 Data points:', result.data.length);
    console.log('📊 MACD points:', result.macdData.length);
    console.log('🏷️ Symbol:', result.symbol);
    console.log('🕐 Last updated:', result.lastUpdated);
    
    if (result.data.length > 0) {
      const latestPrice = result.data[result.data.length - 1];
      console.log('💰 Latest price data:', {
        time: new Date(latestPrice.time * 1000).toLocaleString(),
        open: latestPrice.open,
        high: latestPrice.high,
        low: latestPrice.low,
        close: latestPrice.close
      });
      console.log(`💵 Current BTC Price: $${latestPrice.close.toLocaleString()}`);
    }
    
    return result;
  } catch (error) {
    console.error('❌ API Test Failed:', error);
    return null;
  }
};

console.log('🔧 Browser API test loaded. Run testBitcoinAPI() in console to test.');
