import { describe, it, expect } from 'vitest';
import { getChartData } from '@/data/apis';

describe('Data Length Fix Verification', () => {
  it('should verify increased data points with 365-day request', async () => {
    console.log('🔍 Testing data length fix with 365-day request...');
    
    const result = await getChartData('bitcoin', 365, true);
    
    console.log('📊 Enhanced Data Results:');
    console.log(`   Source: ${result.source}`);
    console.log(`   Symbol: ${result.symbol}`);
    console.log(`   Data points: ${result.data.length}`);
    console.log(`   MACD points: ${result.macdData.length}`);
    console.log(`   Last updated: ${result.lastUpdated}`);
    
    // Should have significantly more data points now
    expect(result.data.length).toBeGreaterThan(300); // Expect at least 300 days of data
    expect(result.macdData.length).toBeGreaterThan(0); // MACD should not be empty
    
    if (result.data.length > 0) {
      const firstPoint = result.data[0];
      const lastPoint = result.data[result.data.length - 1];
      const timeSpan = (lastPoint.time as number) - (firstPoint.time as number);
      const daysSpan = timeSpan / (24 * 60 * 60);
      
      console.log('📅 Time Range Analysis:');
      console.log(`   Start: ${new Date((firstPoint.time as number) * 1000).toDateString()}`);
      console.log(`   End: ${new Date((lastPoint.time as number) * 1000).toDateString()}`);
      console.log(`   Span: ${daysSpan.toFixed(1)} days`);
      console.log(`   Data density: ${(result.data.length / daysSpan).toFixed(2)} points/day`);
      
      // Should span close to a full year
      expect(daysSpan).toBeGreaterThan(300); // At least 300 days
      expect(daysSpan).toBeLessThan(400); // But not more than 400 days
    }
    
    console.log('✅ Data length fix verification passed!');
  });

  it('should verify MACD data is properly generated', async () => {
    console.log('📈 Testing MACD data generation...');
    
    const result = await getChartData('bitcoin', 365, true);
    
    console.log('📊 MACD Analysis:');
    console.log(`   MACD points: ${result.macdData.length}`);
    console.log(`   Price points: ${result.data.length}`);
    console.log(`   MACD coverage: ${((result.macdData.length / result.data.length) * 100).toFixed(1)}%`);
    
    // MACD should have data points
    expect(result.macdData.length).toBeGreaterThan(0);
    
    // MACD should cover a significant portion of the price data
    const coverage = result.macdData.length / result.data.length;
    expect(coverage).toBeGreaterThan(0.5); // At least 50% coverage
    
    if (result.macdData.length > 0) {
      const firstMacd = result.macdData[0];
      const lastMacd = result.macdData[result.macdData.length - 1];
      
      console.log('📊 MACD Data Sample:');
      console.log(`   First: time=${new Date((firstMacd.time as number) * 1000).toDateString()}, value=${firstMacd.value.toFixed(4)}`);
      console.log(`   Last: time=${new Date((lastMacd.time as number) * 1000).toDateString()}, value=${lastMacd.value.toFixed(4)}`);
      
      // Validate MACD data structure
      expect(typeof firstMacd.time).toBe('number');
      expect(typeof firstMacd.value).toBe('number');
      expect(typeof lastMacd.time).toBe('number');
      expect(typeof lastMacd.value).toBe('number');
    }
    
    console.log('✅ MACD data generation verification passed!');
  });

  it('should verify chart time scale improvements', async () => {
    console.log('⏰ Testing chart time scale improvements...');
    
    const result = await getChartData('bitcoin', 365, true);
    
    if (result.data.length > 1) {
      // Check time intervals between data points
      const timeIntervals = [];
      for (let i = 1; i < Math.min(result.data.length, 20); i++) {
        const interval = (result.data[i].time as number) - (result.data[i-1].time as number);
        timeIntervals.push(interval);
      }
      
      const avgInterval = timeIntervals.reduce((a, b) => a + b, 0) / timeIntervals.length;
      const avgHours = avgInterval / 3600;
      
      console.log('⏱️ Time Interval Analysis:');
      console.log(`   Average interval: ${avgHours.toFixed(1)} hours`);
      console.log(`   Expected for daily data: ~24 hours`);
      
      // For daily data, intervals should be approximately 24 hours
      expect(avgHours).toBeGreaterThan(20); // At least 20 hours
      expect(avgHours).toBeLessThan(28); // But not more than 28 hours
      
      // Check for consistent intervals (no big gaps)
      const maxInterval = Math.max(...timeIntervals);
      const minInterval = Math.min(...timeIntervals);
      const intervalVariance = (maxInterval - minInterval) / avgInterval;
      
      console.log(`   Interval variance: ${(intervalVariance * 100).toFixed(1)}%`);
      
      // Variance should be reasonable for daily data
      expect(intervalVariance).toBeLessThan(0.5); // Less than 50% variance
    }
    
    console.log('✅ Time scale verification passed!');
  });

  it('should compare data richness before and after fix', async () => {
    console.log('📊 Comparing data richness...');
    
    // Test with the new 365-day request
    const richData = await getChartData('bitcoin', 365, true);
    
    // Test with the old 200-day request for comparison
    const standardData = await getChartData('bitcoin', 200, true);
    
    console.log('📈 Data Richness Comparison:');
    console.log(`   365-day request: ${richData.data.length} points`);
    console.log(`   200-day request: ${standardData.data.length} points`);
    console.log(`   Improvement: ${richData.data.length - standardData.data.length} additional points`);
    console.log(`   Percentage increase: ${(((richData.data.length - standardData.data.length) / standardData.data.length) * 100).toFixed(1)}%`);
    
    // 365-day should have more data than 200-day
    expect(richData.data.length).toBeGreaterThan(standardData.data.length);
    
    // Should have at least 50% more data
    const improvement = (richData.data.length - standardData.data.length) / standardData.data.length;
    expect(improvement).toBeGreaterThan(0.5);
    
    console.log('✅ Data richness comparison passed!');
  });

  it('should verify current Bitcoin price is displayed correctly', async () => {
    console.log('💰 Verifying current Bitcoin price display...');
    
    const result = await getChartData('bitcoin', 365, true);
    
    if (result.data.length > 0) {
      const latestPrice = result.data[result.data.length - 1];
      const currentPrice = latestPrice.close;
      
      console.log('💵 Current Bitcoin Price Data:');
      console.log(`   Price: $${currentPrice.toLocaleString()}`);
      console.log(`   Time: ${new Date((latestPrice.time as number) * 1000).toLocaleString()}`);
      console.log(`   OHLC: O=$${latestPrice.open.toLocaleString()}, H=$${latestPrice.high.toLocaleString()}, L=$${latestPrice.low.toLocaleString()}, C=$${latestPrice.close.toLocaleString()}`);
      
      // Price should be in reasonable range for Bitcoin (around $118,250)
      expect(currentPrice).toBeGreaterThan(100000); // At least $100k
      expect(currentPrice).toBeLessThan(150000); // But less than $150k
      
      // OHLC relationships should be valid
      expect(latestPrice.high).toBeGreaterThanOrEqual(latestPrice.open);
      expect(latestPrice.high).toBeGreaterThanOrEqual(latestPrice.close);
      expect(latestPrice.low).toBeLessThanOrEqual(latestPrice.open);
      expect(latestPrice.low).toBeLessThanOrEqual(latestPrice.close);
      
      console.log('✅ Bitcoin price validation passed!');
    }
  });
});
