import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getChartData } from '@/data/apis';

describe('Chart Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should fetch real Bitcoin data from CoinGecko API', async () => {
    const result = await getChartData('bitcoin', 7, false);
    
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.macdData).toBeDefined();
    expect(result.symbol).toBe('BITCOIN');
    expect(result.data.length).toBeGreaterThan(0);
    expect(result.macdData.length).toBeGreaterThan(0);
    
    // Check if we got real data (not fallback)
    if (result.source === 'api') {
      console.log('✅ Successfully fetched real Bitcoin data');
      console.log(`📊 Data points: ${result.data.length}`);
      console.log(`📈 MACD points: ${result.macdData.length}`);
      console.log(`💰 Latest price: $${result.data[result.data.length - 1]?.close.toLocaleString()}`);
      console.log(`🕐 Last updated: ${result.lastUpdated}`);
    } else {
      console.log('⚠️ Using fallback mock data');
    }
  });

  it('should return valid price data structure', async () => {
    const result = await getChartData('bitcoin', 7, false);
    
    // Validate data structure
    result.data.forEach((dataPoint, index) => {
      expect(dataPoint).toHaveProperty('time');
      expect(dataPoint).toHaveProperty('open');
      expect(dataPoint).toHaveProperty('high');
      expect(dataPoint).toHaveProperty('low');
      expect(dataPoint).toHaveProperty('close');
      
      expect(typeof dataPoint.time).toBe('number');
      expect(typeof dataPoint.open).toBe('number');
      expect(typeof dataPoint.high).toBe('number');
      expect(typeof dataPoint.low).toBe('number');
      expect(typeof dataPoint.close).toBe('number');
      
      // Validate OHLC relationships
      expect(dataPoint.high).toBeGreaterThanOrEqual(dataPoint.open);
      expect(dataPoint.high).toBeGreaterThanOrEqual(dataPoint.close);
      expect(dataPoint.low).toBeLessThanOrEqual(dataPoint.open);
      expect(dataPoint.low).toBeLessThanOrEqual(dataPoint.close);
    });
  });

  it('should return valid MACD data structure', async () => {
    const result = await getChartData('bitcoin', 7, false);
    
    // Validate MACD data structure
    result.macdData.forEach((macdPoint) => {
      expect(macdPoint).toHaveProperty('time');
      expect(macdPoint).toHaveProperty('value');
      
      expect(typeof macdPoint.time).toBe('number');
      expect(typeof macdPoint.value).toBe('number');
    });
  });

  it('should handle API errors gracefully with fallback', async () => {
    // Test with invalid symbol to trigger fallback
    const result = await getChartData('invalid-symbol-that-does-not-exist', 7, false);
    
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.macdData).toBeDefined();
    expect(result.source).toBe('mock'); // Should fallback to mock data
    expect(result.data.length).toBeGreaterThan(0);
    expect(result.macdData.length).toBeGreaterThan(0);
  });
});
