import { describe, it, expect, beforeEach } from 'vitest';
import { bitcoinDataManager, getBitcoinDataForInterval, refreshBitcoinData, getBitcoinStoreInfo } from '@/data/bitcoinDataStore';

describe('Bitcoin Database Functionality', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  it('should fetch and store comprehensive Bitcoin data', async () => {
    console.log('🏪 Testing comprehensive Bitcoin data storage...');
    
    const storeData = await bitcoinDataManager.getComprehensiveData();
    
    console.log('📊 Comprehensive Data Results:');
    console.log(`   Data points: ${storeData.data.length}`);
    console.log(`   MACD points: ${storeData.macdData.length}`);
    console.log(`   Symbol: ${storeData.symbol}`);
    console.log(`   Source: ${storeData.source}`);
    console.log(`   Last updated: ${storeData.lastUpdated}`);
    
    // Verify comprehensive data
    expect(storeData.data.length).toBeGreaterThan(100); // Should have substantial data
    expect(storeData.macdData.length).toBeGreaterThan(0);
    expect(storeData.symbol.toLowerCase()).toBe('bitcoin');
    expect(storeData.source).toBe('api');
    expect(storeData.lastUpdated).toBeTruthy();
    
    // Verify data structure
    if (storeData.data.length > 0) {
      const firstPoint = storeData.data[0];
      const lastPoint = storeData.data[storeData.data.length - 1];
      
      expect(firstPoint).toHaveProperty('time');
      expect(firstPoint).toHaveProperty('open');
      expect(firstPoint).toHaveProperty('high');
      expect(firstPoint).toHaveProperty('low');
      expect(firstPoint).toHaveProperty('close');
      
      console.log(`   Time range: ${new Date((firstPoint.time as number) * 1000).toDateString()} to ${new Date((lastPoint.time as number) * 1000).toDateString()}`);
    }
    
    console.log('✅ Comprehensive data storage verified');
  });

  it('should filter data correctly for all time intervals', async () => {
    console.log('🔍 Testing data filtering for all time intervals...');
    
    // Ensure we have comprehensive data
    await bitcoinDataManager.getComprehensiveData();
    
    const intervals = ['12h', '1D', '2D', '3D', '1W'] as const;
    
    for (const interval of intervals) {
      console.log(`\n📊 Testing ${interval} interval:`);
      
      const result = await getBitcoinDataForInterval(interval);
      
      console.log(`   Filtered data points: ${result.data.length}`);
      console.log(`   Filtered MACD points: ${result.macdData.length}`);
      
      // Verify we get data
      expect(result.data.length).toBeGreaterThan(0);
      expect(result.macdData.length).toBeGreaterThan(0);
      
      // Verify time filtering worked correctly
      if (result.data.length > 0) {
        const firstTime = result.data[0].time as number;
        const lastTime = result.data[result.data.length - 1].time as number;
        const timeSpan = lastTime - firstTime;
        const hoursSpan = timeSpan / 3600;
        
        console.log(`   Time span: ${hoursSpan.toFixed(1)} hours`);
        console.log(`   From: ${new Date(firstTime * 1000).toISOString()}`);
        console.log(`   To: ${new Date(lastTime * 1000).toISOString()}`);
        
        // Verify time span is reasonable for the interval
        switch (interval) {
          case '12h':
            expect(hoursSpan).toBeLessThanOrEqual(24); // Should be within 24 hours
            break;
          case '1D':
            expect(hoursSpan).toBeLessThanOrEqual(48); // Should be within 48 hours
            break;
          case '2D':
            expect(hoursSpan).toBeLessThanOrEqual(72); // Should be within 72 hours
            break;
          case '3D':
            expect(hoursSpan).toBeLessThanOrEqual(96); // Should be within 96 hours
            break;
          case '1W':
            expect(hoursSpan).toBeLessThanOrEqual(192); // Should be within 192 hours (8 days)
            break;
        }
      }
      
      console.log(`   ✅ ${interval} interval filtering verified`);
    }
    
    console.log('\n✅ All time interval filtering verified');
  });

  it('should use localStorage caching correctly', async () => {
    console.log('💾 Testing localStorage caching...');
    
    // First call should fetch from API
    console.log('📡 First call (should fetch from API)...');
    const firstCall = await bitcoinDataManager.getComprehensiveData();
    
    // Verify data was stored
    const stored = localStorage.getItem('bitcoin_data_store');
    expect(stored).toBeTruthy();
    
    const parsedStored = JSON.parse(stored!);
    expect(parsedStored.data.length).toBe(firstCall.data.length);
    expect(parsedStored.symbol).toBe(firstCall.symbol);
    
    console.log('💾 Data stored in localStorage:', {
      dataPoints: parsedStored.data.length,
      macdPoints: parsedStored.macdData.length,
      lastUpdated: parsedStored.lastUpdated
    });
    
    // Second call should use cache
    console.log('📂 Second call (should use cache)...');
    const secondCall = await bitcoinDataManager.getComprehensiveData();
    
    // Should be identical data
    expect(secondCall.data.length).toBe(firstCall.data.length);
    expect(secondCall.lastUpdated).toBe(firstCall.lastUpdated);
    
    console.log('✅ localStorage caching verified');
  });

  it('should handle cache expiration correctly', async () => {
    console.log('⏰ Testing cache expiration...');
    
    // Get initial data
    const initialData = await bitcoinDataManager.getComprehensiveData();
    
    // Manually modify the stored data to be expired
    const stored = localStorage.getItem('bitcoin_data_store');
    if (stored) {
      const parsedStored = JSON.parse(stored);
      // Set last updated to 2 hours ago (beyond 30 minute cache)
      parsedStored.lastUpdated = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString();
      localStorage.setItem('bitcoin_data_store', JSON.stringify(parsedStored));
      
      console.log('🕐 Modified cache to be expired');
    }
    
    // Next call should fetch fresh data
    console.log('📡 Call with expired cache (should fetch fresh)...');
    const freshData = await bitcoinDataManager.getComprehensiveData();
    
    // Should have fresh timestamp
    const freshTimestamp = new Date(freshData.lastUpdated).getTime();
    const initialTimestamp = new Date(initialData.lastUpdated).getTime();
    
    expect(freshTimestamp).toBeGreaterThan(initialTimestamp);
    
    console.log('✅ Cache expiration handling verified');
  });

  it('should provide store info correctly', async () => {
    console.log('ℹ️ Testing store info functionality...');
    
    // Initially should have no data
    let storeInfo = getBitcoinStoreInfo();
    console.log('📊 Initial store info:', storeInfo);
    
    expect(storeInfo.hasData).toBe(false);
    expect(storeInfo.dataPoints).toBe(0);
    expect(storeInfo.macdPoints).toBe(0);
    expect(storeInfo.lastUpdated).toBeNull();
    
    // After loading data
    await bitcoinDataManager.getComprehensiveData();
    storeInfo = getBitcoinStoreInfo();
    
    console.log('📊 Store info after loading:', storeInfo);
    
    expect(storeInfo.hasData).toBe(true);
    expect(storeInfo.dataPoints).toBeGreaterThan(0);
    expect(storeInfo.macdPoints).toBeGreaterThan(0);
    expect(storeInfo.lastUpdated).toBeTruthy();
    
    console.log('✅ Store info functionality verified');
  });

  it('should handle refresh functionality', async () => {
    console.log('🔄 Testing data refresh functionality...');
    
    // Get initial data
    const initialData = await bitcoinDataManager.getComprehensiveData();
    console.log('📊 Initial data timestamp:', initialData.lastUpdated);
    
    // Wait a moment to ensure timestamp difference
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Force refresh
    console.log('🔄 Forcing data refresh...');
    const refreshedData = await refreshBitcoinData();
    
    console.log('📊 Refreshed data timestamp:', refreshedData.lastUpdated);
    
    // Should have newer timestamp
    const initialTime = new Date(initialData.lastUpdated).getTime();
    const refreshedTime = new Date(refreshedData.lastUpdated).getTime();
    
    expect(refreshedTime).toBeGreaterThan(initialTime);
    expect(refreshedData.data.length).toBeGreaterThan(0);
    expect(refreshedData.macdData.length).toBeGreaterThan(0);
    
    console.log('✅ Data refresh functionality verified');
  });

  it('should demonstrate performance improvement', async () => {
    console.log('⚡ Testing performance improvement...');
    
    // Ensure we have comprehensive data loaded
    await bitcoinDataManager.getComprehensiveData();
    
    const intervals = ['12h', '1D', '2D', '3D', '1W'] as const;
    const performanceResults = [];
    
    for (const interval of intervals) {
      const startTime = performance.now();
      
      const result = await getBitcoinDataForInterval(interval);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      performanceResults.push({
        interval,
        duration: Math.round(duration),
        dataPoints: result.data.length,
        macdPoints: result.macdData.length
      });
      
      console.log(`   ${interval}: ${Math.round(duration)}ms (${result.data.length} points)`);
      
      // Database filtering should be very fast (under 100ms)
      expect(duration).toBeLessThan(100);
    }
    
    console.log('⚡ Performance Results:');
    performanceResults.forEach(result => {
      console.log(`   ${result.interval}: ${result.duration}ms for ${result.dataPoints} data points`);
    });
    
    const avgDuration = performanceResults.reduce((sum, r) => sum + r.duration, 0) / performanceResults.length;
    console.log(`   Average filtering time: ${Math.round(avgDuration)}ms`);
    
    // Average should be very fast
    expect(avgDuration).toBeLessThan(50);
    
    console.log('✅ Performance improvement verified - instant filtering!');
  });

  it('should document the complete database implementation', () => {
    console.log('📋 Database Implementation Documentation');
    
    const implementation = {
      dataStorage: {
        location: 'localStorage with key "bitcoin_data_store"',
        structure: 'BitcoinDataStore interface with data, macdData, metadata',
        capacity: '200 days of comprehensive Bitcoin price data',
        caching: '30-minute cache duration with automatic refresh'
      },
      filtering: {
        method: 'Client-side time-based filtering from comprehensive dataset',
        intervals: '12h, 1D, 2D, 3D, 1W supported',
        performance: 'Sub-50ms filtering for instant chart updates',
        accuracy: 'Precise time-based cutoff calculations'
      },
      apiIntegration: {
        initialLoad: 'Single API call for 200 days of data',
        caching: 'Intelligent caching with expiration handling',
        fallback: 'Graceful fallback to expired cache on API failure',
        refresh: 'Manual refresh capability for fresh data'
      },
      benefits: {
        performance: 'Instant time interval switching (no API delays)',
        efficiency: 'Reduced API calls (1 call vs 5+ calls)',
        reliability: 'Offline capability with cached data',
        userExperience: 'Smooth, responsive chart interactions'
      }
    };
    
    console.log('\n💾 Data Storage:');
    Object.entries(implementation.dataStorage).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    console.log('\n🔍 Data Filtering:');
    Object.entries(implementation.filtering).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    console.log('\n📡 API Integration:');
    Object.entries(implementation.apiIntegration).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    console.log('\n🚀 Benefits:');
    Object.entries(implementation.benefits).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    expect(Object.keys(implementation).length).toBe(4);
    console.log('\n✅ Database implementation documented');
  });
});
