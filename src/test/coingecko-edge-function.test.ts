import { describe, it, expect, beforeEach, vi } from "vitest";

// Test configuration
const SUPABASE_URL = "https://oyekojzsteofnqqznuzq.supabase.co";
const SUPABASE_ANON_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95ZWtvanpzdGVvZm5xcXpudXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0OTYwOTcsImV4cCI6MjA2ODA3MjA5N30.ahBfBMCyBYx9nqm4bEpfw9-_1LxLbWYHgPnxAwzF7xY";
const EDGE_FUNCTION_URL = `${SUPABASE_URL}/functions/v1/coingecko-data`;

// Expected BTC price reference (approximately $118,259.70 USD)
const EXPECTED_BTC_PRICE_RANGE = {
  min: 110000, // Allow 10% variance
  max: 130000,
};

interface CoinGeckoResponse {
  prices: number[][];
  market_caps: number[][];
  total_volumes: number[][];
}

describe("CoinGecko Edge Function Integration Tests", () => {
  beforeEach(() => {
    // Reset any mocks before each test
    vi.clearAllMocks();
  });

  describe("Basic Function Tests", () => {
    it("should respond to POST requests with valid Bitcoin data", async () => {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          symbol: "bitcoin",
          days: 7,
          interval: "daily",
        }),
      });

      expect(response.ok).toBe(true);
      expect(response.status).toBe(200);
      expect(response.headers.get("content-type")).toContain("application/json");
    });

    it("should return proper data structure", async () => {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          symbol: "bitcoin",
          days: 7,
          interval: "daily",
        }),
      });

      const data: CoinGeckoResponse = await response.json();

      // Validate response structure
      expect(data).toHaveProperty("prices");
      expect(data).toHaveProperty("market_caps");
      expect(data).toHaveProperty("total_volumes");

      // Validate data types
      expect(Array.isArray(data.prices)).toBe(true);
      expect(Array.isArray(data.market_caps)).toBe(true);
      expect(Array.isArray(data.total_volumes)).toBe(true);

      // Validate data content
      expect(data.prices.length).toBeGreaterThan(0);
      expect(data.market_caps.length).toBeGreaterThan(0);
      expect(data.total_volumes.length).toBeGreaterThan(0);
    });

    it("should return current BTC price within expected range", async () => {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          symbol: "bitcoin",
          days: 1,
          interval: "daily",
        }),
      });

      const data: CoinGeckoResponse = await response.json();

      // Get the most recent price (last item in the array)
      const latestPrice = data.prices[data.prices.length - 1];
      expect(latestPrice).toBeDefined();
      expect(latestPrice.length).toBe(2); // [timestamp, price]

      const currentPrice = latestPrice[1];
      expect(currentPrice).toBeGreaterThan(EXPECTED_BTC_PRICE_RANGE.min);
      expect(currentPrice).toBeLessThan(EXPECTED_BTC_PRICE_RANGE.max);

      // Log the actual price for reference
      console.log(`Current BTC price: $${currentPrice.toLocaleString()}`);
    });
  });

  describe("Error Handling Tests", () => {
    it("should reject GET requests", async () => {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
      });

      expect(response.status).toBe(405);
      const data = await response.json();
      expect(data).toHaveProperty("error", "Method not allowed");
    });

    it("should handle OPTIONS requests (CORS preflight)", async () => {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "OPTIONS",
      });

      expect(response.status).toBe(200);
      expect(response.headers.get("Access-Control-Allow-Origin")).toBe("*");
      expect(response.headers.get("Access-Control-Allow-Methods")).toContain("POST");
    });

    it("should validate symbol parameter", async () => {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          symbol: "INVALID_SYMBOL_WITH_CAPS",
          days: 7,
          interval: "daily",
        }),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("error", "Invalid symbol");
    });
  });

  describe("Data Validation Tests", () => {
    it("should return valid price data format", async () => {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          symbol: "bitcoin",
          days: 7,
          interval: "daily",
        }),
      });

      const data: CoinGeckoResponse = await response.json();

      // Validate each price entry
      data.prices.forEach((priceEntry, index) => {
        expect(priceEntry).toHaveLength(2);
        expect(typeof priceEntry[0]).toBe("number"); // timestamp
        expect(typeof priceEntry[1]).toBe("number"); // price
        expect(priceEntry[0]).toBeGreaterThan(0);
        expect(priceEntry[1]).toBeGreaterThan(0);
      });
    });

    it("should return data for requested number of days", async () => {
      const requestedDays = 7;
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          symbol: "bitcoin",
          days: requestedDays,
          interval: "daily",
        }),
      });

      const data: CoinGeckoResponse = await response.json();

      // Should have approximately the requested number of days (allow some variance)
      expect(data.prices.length).toBeGreaterThanOrEqual(requestedDays - 1);
      expect(data.prices.length).toBeLessThanOrEqual(requestedDays + 2);
    });
  });

  describe("Caching Tests", () => {
    it("should return cached data on subsequent requests", async () => {
      const requestBody = {
        symbol: "bitcoin",
        days: 1,
        interval: "daily",
      };

      // First request
      const response1 = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify(requestBody),
      });

      expect(response1.ok).toBe(true);
      const data1 = await response1.json();

      // Second request (should be cached)
      const response2 = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify(requestBody),
      });

      expect(response2.ok).toBe(true);
      const data2 = await response2.json();

      // Data should be identical (from cache)
      expect(data1).toEqual(data2);

      // Check for cache header if available
      const cacheHeader = response2.headers.get("X-Cache");
      if (cacheHeader) {
        // Cache might be HIT or MISS depending on timing and edge function behavior
        expect(["HIT", "MISS"]).toContain(cacheHeader);
      }
    });
  });

  describe("Performance Tests", () => {
    it("should respond within reasonable time", async () => {
      const startTime = Date.now();

      const response = await fetch(EDGE_FUNCTION_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          symbol: "bitcoin",
          days: 7,
          interval: "daily",
        }),
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.ok).toBe(true);
      expect(responseTime).toBeLessThan(10000); // Should respond within 10 seconds

      console.log(`Response time: ${responseTime}ms`);
    });
  });
});
