import { CandlestickData, HistogramData, UTCTimestamp } from "lightweight-charts";

// Enhanced TypeScript interfaces for chart data
export interface ChartDataPoint {
  time: UTCTimestamp;
  open: number;
  high: number;
  low: number;
  close: number;
}

export interface MACDDataPoint {
  time: UTCTimestamp;
  value: number;
  color?: string;
}

export interface DataGenerationOptions {
  dataPoints?: number;
  startDate?: Date;
  basePrice?: number;
  volatility?: number;
}

export interface MACDGenerationOptions {
  dataPoints?: number;
  startDate?: Date;
  amplitude?: number;
  frequency?: number;
  randomness?: number;
}

// Generate fake candlestick data with enhanced options
export const generateFakeData = (
  options: DataGenerationOptions = {}
): CandlestickData[] => {
  const {
    dataPoints = 100,
    startDate = new Date(2024, 0, 1),
    basePrice = 100,
    volatility = 0.02,
  } = options;

  const data: CandlestickData[] = [];
  let currentPrice = basePrice;

  for (let i = 0; i < dataPoints; i++) {
    const time = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000).getTime() / 1000;
    const change = (Math.random() - 0.5) * volatility * currentPrice;

    const open = currentPrice;
    const close = currentPrice + change;
    const high = Math.max(open, close) + Math.random() * 0.01 * currentPrice;
    const low = Math.min(open, close) - Math.random() * 0.01 * currentPrice;

    // Ensure high >= max(open, close) and low <= min(open, close)
    const validHigh = Math.max(high, open, close);
    const validLow = Math.min(low, open, close);

    data.push({
      time: time as UTCTimestamp,
      open: Number(open.toFixed(2)),
      high: Number(validHigh.toFixed(2)),
      low: Number(validLow.toFixed(2)),
      close: Number(close.toFixed(2)),
    });

    currentPrice = close;
  }

  return data;
};

// Generate fake MACD data with enhanced options
export const generateFakeMACDData = (
  options: MACDGenerationOptions = {}
): HistogramData[] => {
  const {
    dataPoints = 100,
    startDate = new Date(2024, 0, 1),
    amplitude = 2,
    frequency = 0.1,
    randomness = 0.5,
  } = options;

  const data: HistogramData[] = [];

  for (let i = 0; i < dataPoints; i++) {
    const time = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000).getTime() / 1000;
    const value =
      Math.sin(i * frequency) * amplitude + (Math.random() - 0.5) * randomness;

    data.push({
      time: time as UTCTimestamp,
      value: Number(value.toFixed(4)),
      color: value > 0 ? "#2196f3" : "#ef5350", // TradingView MACD colors
    });
  }

  return data;
};

// Enhanced data validation with detailed error reporting
export const validateChartData = (
  data: CandlestickData[]
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!Array.isArray(data)) {
    errors.push("Data must be an array");
    return { isValid: false, errors };
  }

  if (data.length === 0) {
    errors.push("Data array cannot be empty");
    return { isValid: false, errors };
  }

  data.forEach((point, index) => {
    if (typeof point.time !== "number") {
      errors.push(
        `Invalid time at index ${index}: expected number, got ${typeof point.time}`
      );
    }
    if (typeof point.open !== "number" || isNaN(point.open)) {
      errors.push(
        `Invalid open price at index ${index}: expected number, got ${typeof point.open}`
      );
    }
    if (typeof point.high !== "number" || isNaN(point.high)) {
      errors.push(
        `Invalid high price at index ${index}: expected number, got ${typeof point.high}`
      );
    }
    if (typeof point.low !== "number" || isNaN(point.low)) {
      errors.push(
        `Invalid low price at index ${index}: expected number, got ${typeof point.low}`
      );
    }
    if (typeof point.close !== "number" || isNaN(point.close)) {
      errors.push(
        `Invalid close price at index ${index}: expected number, got ${typeof point.close}`
      );
    }

    // Validate OHLC relationships
    if (point.high < Math.max(point.open, point.close)) {
      errors.push(`Invalid OHLC at index ${index}: high must be >= max(open, close)`);
    }
    if (point.low > Math.min(point.open, point.close)) {
      errors.push(`Invalid OHLC at index ${index}: low must be <= min(open, close)`);
    }
  });

  return { isValid: errors.length === 0, errors };
};

// Validate MACD data
export const validateMACDData = (
  data: HistogramData[]
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!Array.isArray(data)) {
    errors.push("MACD data must be an array");
    return { isValid: false, errors };
  }

  if (data.length === 0) {
    errors.push("MACD data array cannot be empty");
    return { isValid: false, errors };
  }

  data.forEach((point, index) => {
    if (typeof point.time !== "number") {
      errors.push(
        `Invalid time at MACD index ${index}: expected number, got ${typeof point.time}`
      );
    }
    if (typeof point.value !== "number" || isNaN(point.value)) {
      errors.push(
        `Invalid value at MACD index ${index}: expected number, got ${typeof point.value}`
      );
    }
    if (point.color && typeof point.color !== "string") {
      errors.push(
        `Invalid color at MACD index ${index}: expected string, got ${typeof point.color}`
      );
    }
  });

  return { isValid: errors.length === 0, errors };
};

// Data transformation utilities
export const sortDataByTime = (data: CandlestickData[]): CandlestickData[] => {
  return [...data].sort((a, b) => (a.time as number) - (b.time as number));
};

export const sortMACDDataByTime = (data: HistogramData[]): HistogramData[] => {
  return [...data].sort((a, b) => (a.time as number) - (b.time as number));
};

// Filter data by date range
export const filterDataByDateRange = (
  data: CandlestickData[],
  startDate: Date,
  endDate: Date
): CandlestickData[] => {
  const startTime = startDate.getTime() / 1000;
  const endTime = endDate.getTime() / 1000;

  return data.filter(
    (point) => (point.time as number) >= startTime && (point.time as number) <= endTime
  );
};

// Calculate basic statistics
export const calculateDataStats = (data: CandlestickData[]) => {
  if (data.length === 0) {
    return {
      min: 0,
      max: 0,
      average: 0,
      volatility: 0,
      change: 0,
      changePercent: 0,
    };
  }

  const prices = data.map((d) => d.close);
  const min = Math.min(...prices);
  const max = Math.max(...prices);
  const average = prices.reduce((sum, price) => sum + price, 0) / prices.length;

  // Calculate volatility (standard deviation)
  const variance =
    prices.reduce((sum, price) => sum + Math.pow(price - average, 2), 0) / prices.length;
  const volatility = Math.sqrt(variance);

  // Calculate change from first to last
  const firstPrice = data[0].close;
  const lastPrice = data[data.length - 1].close;
  const change = lastPrice - firstPrice;
  const changePercent = (change / firstPrice) * 100;

  return {
    min: Number(min.toFixed(2)),
    max: Number(max.toFixed(2)),
    average: Number(average.toFixed(2)),
    volatility: Number(volatility.toFixed(4)),
    change: Number(change.toFixed(2)),
    changePercent: Number(changePercent.toFixed(2)),
  };
};

// Transform CoinGecko API response to chart format
// CoinGecko returns: [[timestamp, price], [timestamp, price], ...]
export const transformCoinGeckoData = (apiData: number[][]): CandlestickData[] => {
  if (!Array.isArray(apiData) || apiData.length === 0) {
    return [];
  }

  return apiData.map((point, index) => {
    if (!Array.isArray(point) || point.length < 2) {
      throw new Error(`Invalid CoinGecko data point at index ${index}`);
    }

    const [timestamp, price] = point;
    const time = Math.floor(timestamp / 1000) as UTCTimestamp;

    // For daily data, we simulate OHLC from single price point
    // In real implementation, we'd use actual OHLC data
    const volatility = 0.005; // 0.5% volatility simulation
    const randomFactor = (Math.random() - 0.5) * volatility;

    const open = price * (1 + randomFactor);
    const close = price;
    const high = Math.max(open, close) * (1 + Math.random() * volatility);
    const low = Math.min(open, close) * (1 - Math.random() * volatility);

    return {
      time,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
    };
  });
};

// Calculate Exponential Moving Average
const calculateEMA = (prices: number[], period: number): number[] => {
  const ema: number[] = [];
  const multiplier = 2 / (period + 1);

  // Start with SMA for the first value
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += prices[i];
  }
  ema[period - 1] = sum / period;

  // Calculate EMA for the rest
  for (let i = period; i < prices.length; i++) {
    ema[i] = (prices[i] - ema[i - 1]) * multiplier + ema[i - 1];
  }

  return ema;
};

// Enhanced MACD calculation with proper EMA and signal line
export interface MACDResult {
  macdLine: { time: number; value: number }[];
  signalLine: { time: number; value: number }[];
  histogram: HistogramData[];
}

export const calculateMACDFromPrices = (data: CandlestickData[]): MACDResult => {
  if (data.length < 35) {
    // Need at least 35 periods for proper MACD with signal
    return {
      macdLine: [],
      signalLine: [],
      histogram: [],
    };
  }

  const prices = data.map((d) => d.close);

  // Calculate EMAs
  const ema12 = calculateEMA(prices, 12);
  const ema26 = calculateEMA(prices, 26);

  // Calculate MACD line (12 EMA - 26 EMA)
  const macdValues: number[] = [];
  const macdLine: { time: number; value: number }[] = [];

  for (let i = 25; i < prices.length; i++) {
    // Start from index 25 (26th element)
    const macdValue = ema12[i] - ema26[i];
    macdValues.push(macdValue);
    macdLine.push({
      time: data[i].time as number,
      value: Number(macdValue.toFixed(6)),
    });
  }

  // Calculate Signal line (9 EMA of MACD line)
  const signalEMA = calculateEMA(macdValues, 9);
  const signalLine: { time: number; value: number }[] = [];
  const histogram: HistogramData[] = [];

  for (let i = 8; i < macdValues.length; i++) {
    // Start from index 8 (9th element of MACD)
    const signalValue = signalEMA[i];
    const macdValue = macdValues[i];
    const histogramValue = macdValue - signalValue;
    const dataIndex = i + 25; // Adjust for original data index

    signalLine.push({
      time: data[dataIndex].time as number,
      value: Number(signalValue.toFixed(6)),
    });

    histogram.push({
      time: data[dataIndex].time,
      value: Number(histogramValue.toFixed(6)),
      color: histogramValue > 0 ? "#2196f3" : "#ef5350",
    });
  }

  return {
    macdLine,
    signalLine,
    histogram,
  };
};

// Legacy function for backward compatibility
export const calculateMACDHistogram = (data: CandlestickData[]): HistogramData[] => {
  const result = calculateMACDFromPrices(data);
  return result.histogram;
};

// Error handling wrapper for data generation
export const safeGenerateData = (
  generator: () => CandlestickData[],
  fallbackData?: CandlestickData[]
): CandlestickData[] => {
  try {
    const data = generator();
    const validation = validateChartData(data);

    if (!validation.isValid) {
      console.warn("Generated data validation failed:", validation.errors);
      return fallbackData || generateFakeData();
    }

    return data;
  } catch (error) {
    console.error("Data generation failed:", error);
    return fallbackData || generateFakeData();
  }
};
