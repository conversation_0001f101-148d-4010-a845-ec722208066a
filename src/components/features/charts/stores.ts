import { useState, useCallback } from "react";
import { CandlestickData, HistogramData } from "lightweight-charts";
import {
  generateFakeData,
  generateFakeMACDData,
  validateChartData,
  validateMACDData,
  calculateDataStats,
  safeGenerateData,
  DataGenerationOptions,
  MACDGenerationOptions,
} from "./utils";

// Chart state interfaces
export interface ChartState {
  data: CandlestickData[];
  macdData: HistogramData[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  stats: ReturnType<typeof calculateDataStats>;
}

export interface ChartOptions {
  height: number;
  showMACD: boolean;
  theme: "light" | "dark";
  autoRefresh: boolean;
  refreshInterval: number; // in milliseconds
  dataSource: "mock" | "api";
  symbol: string;
}

export interface ChartDataSource {
  type: "mock" | "coingecko";
  symbol?: string;
  days?: number;
  interval?: string;
}

// Default chart options
const defaultChartOptions: ChartOptions = {
  height: 400,
  showMACD: true,
  theme: "dark",
  autoRefresh: false,
  refreshInterval: 300000, // 5 minutes
  dataSource: "mock",
  symbol: "BTC",
};

// Chart state hook
export const useChartState = (
  initialData?: CandlestickData[],
  initialMACDData?: HistogramData[]
) => {
  const data = initialData || [];
  const macdData = initialMACDData || [];

  const [state, setState] = useState<ChartState>({
    data,
    macdData,
    isLoading: false,
    error: null,
    lastUpdated: null,
    stats: calculateDataStats(data),
  });

  const [options, setOptions] = useState<ChartOptions>(defaultChartOptions);

  const updateData = useCallback(
    (newData: CandlestickData[], newMacdData: HistogramData[]) => {
      // Validate data before updating
      const dataValidation = validateChartData(newData);
      const macdValidation = validateMACDData(newMacdData);

      if (!dataValidation.isValid) {
        setState((prev) => ({
          ...prev,
          error: `Invalid chart data: ${dataValidation.errors.join(", ")}`,
          isLoading: false,
        }));
        return;
      }

      if (!macdValidation.isValid) {
        setState((prev) => ({
          ...prev,
          error: `Invalid MACD data: ${macdValidation.errors.join(", ")}`,
          isLoading: false,
        }));
        return;
      }

      setState((prev) => ({
        ...prev,
        data: newData,
        macdData: newMacdData,
        lastUpdated: new Date(),
        error: null,
        stats: calculateDataStats(newData),
        isLoading: false,
      }));
    },
    []
  );

  const setLoading = useCallback((isLoading: boolean) => {
    setState((prev) => ({ ...prev, isLoading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState((prev) => ({ ...prev, error, isLoading: false }));
  }, []);

  const updateOptions = useCallback((newOptions: Partial<ChartOptions>) => {
    setOptions((prev) => ({ ...prev, ...newOptions }));
  }, []);

  // Generate mock data with current options
  const generateMockData = useCallback(
    (dataOptions?: DataGenerationOptions, macdOptions?: MACDGenerationOptions) => {
      setLoading(true);

      try {
        const mockData = safeGenerateData(() => generateFakeData(dataOptions));
        const mockMacdData = generateFakeMACDData(macdOptions);

        updateData(mockData, mockMacdData);
      } catch (error) {
        setError(
          `Failed to generate mock data: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
    [updateData, setLoading, setError]
  );

  // Refresh data based on current data source
  const refreshData = useCallback(async () => {
    if (options.dataSource === "mock") {
      generateMockData();
    } else {
      // API data refresh will be implemented in later tasks
      setError("API data refresh not yet implemented");
    }
  }, [options.dataSource, generateMockData, setError]);

  return {
    state,
    options,
    updateData,
    setLoading,
    setError,
    updateOptions,
    generateMockData,
    refreshData,
  };
};

// Chart configuration hook for persistent settings
export const useChartConfig = () => {
  const [config, setConfig] = useState(() => {
    // Load from localStorage if available
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("chartConfig");
      if (saved) {
        try {
          return JSON.parse(saved);
        } catch {
          return defaultChartOptions;
        }
      }
    }
    return defaultChartOptions;
  });

  const updateConfig = useCallback(
    (newConfig: Partial<ChartOptions>) => {
      const updated = { ...config, ...newConfig };
      setConfig(updated);

      // Save to localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem("chartConfig", JSON.stringify(updated));
      }
    },
    [config]
  );

  return { config, updateConfig };
};
