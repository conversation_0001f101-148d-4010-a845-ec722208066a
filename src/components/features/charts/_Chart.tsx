import React, { useEffect, useRef, memo, useMemo } from "react";
import {
  create<PERSON>hart,
  ColorType,
  IChartApi,
  CandlestickData,
  HistogramData,
  CandlestickSeries,
  HistogramSeries,
  LineSeries,
  CrosshairMode,
  LineStyle,
  UTCTimestamp,
} from "lightweight-charts";
import { useChartState, ChartOptions } from "./stores";
import { calculateMACDFromPrices } from "./utils";

interface ChartProps {
  data?: CandlestickData[];
  macdData?: HistogramData[];
  width?: number;
  height?: number;
  options?: Partial<ChartOptions>;
  onDataUpdate?: (data: CandlestickData[], macdData: HistogramData[]) => void;
  className?: string;
}

export const Chart = memo(
  ({
    data: propData,
    macdData: propMacdData,
    width,
    height,
    options: propOptions,
    onDataUpdate,
    className = "",
  }: ChartProps) => {
    const chartContainerRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<IChartApi | null>(null);

    // Use internal state management if no external data provided
    const {
      state,
      options,
      updateOptions,
      generateMockData,
      fetchRealData,
      refreshData,
    } = useChartState(propData, propMacdData);

    // Use prop options if provided, otherwise use state options
    const chartOptions = useMemo(
      () => (propOptions ? { ...options, ...propOptions } : options),
      [options, propOptions]
    );
    const chartHeight = height || chartOptions.height;

    // Use external data if provided, otherwise use internal state
    const chartData = propData || state.data;
    const chartMacdData = propMacdData || state.macdData;

    // Generate initial data if none provided
    useEffect(() => {
      if (!propData && !propMacdData && state.data.length === 0) {
        if (options.dataSource === "api") {
          fetchRealData();
        } else {
          generateMockData();
        }
      }
    }, [
      propData,
      propMacdData,
      state.data.length,
      options.dataSource,
      fetchRealData,
      generateMockData,
    ]);

    // Update options when prop options change
    useEffect(() => {
      if (propOptions) {
        updateOptions(propOptions);
      }
    }, [propOptions, updateOptions]);

    // Refresh data when data source or symbol changes
    useEffect(() => {
      if (!propData && !propMacdData && state.data.length > 0) {
        refreshData();
      }
    }, [
      options.dataSource,
      options.symbol,
      propData,
      propMacdData,
      state.data.length,
      refreshData,
    ]);

    // Notify parent of data updates
    useEffect(() => {
      if (onDataUpdate && chartData.length > 0 && chartMacdData.length > 0) {
        onDataUpdate(chartData, chartMacdData);
      }
    }, [chartData, chartMacdData, onDataUpdate]);

    useEffect(() => {
      if (!chartContainerRef.current || chartData.length === 0) return;

      // TradingView-style color scheme
      const tradingViewColors = {
        background: "#131722",
        gridLines: "#1e222d",
        textColor: "#d1d4dc",
        borderColor: "#2a2e39",
        bullishGreen: "#26a69a",
        bearishRed: "#ef5350",
        macdBlue: "#2196f3",
        signalOrange: "#ff9800",
      };

      // Create the chart with TradingView-style configuration
      const chart = createChart(chartContainerRef.current, {
        layout: {
          background: {
            type: ColorType.Solid,
            color: tradingViewColors.background,
          },
          textColor: tradingViewColors.textColor,
          fontSize: 12,
          fontFamily: "'Trebuchet MS', Arial, sans-serif",
        },
        grid: {
          vertLines: {
            color: tradingViewColors.gridLines,
            style: LineStyle.Dotted,
            visible: true,
          },
          horzLines: {
            color: tradingViewColors.gridLines,
            style: LineStyle.Dotted,
            visible: true,
          },
        },
        crosshair: {
          mode: CrosshairMode.Normal,
          vertLine: {
            color: tradingViewColors.textColor,
            width: 1,
            style: LineStyle.Dashed,
            labelBackgroundColor: tradingViewColors.background,
          },
          horzLine: {
            color: tradingViewColors.textColor,
            width: 1,
            style: LineStyle.Dashed,
            labelBackgroundColor: tradingViewColors.background,
          },
        },
        rightPriceScale: {
          borderColor: tradingViewColors.borderColor,
          textColor: tradingViewColors.textColor,
          scaleMargins: {
            top: 0.1,
            bottom: 0.1,
          },
        },
        timeScale: {
          borderColor: tradingViewColors.borderColor,
          timeVisible: true,
          secondsVisible: false,
          rightOffset: 12,
          barSpacing: 3,
          minBarSpacing: 0.5,
        },
        width: width || chartContainerRef.current.clientWidth,
        height: chartHeight,
      });

      chartRef.current = chart;

      // Chart will use 70% for main price chart and 30% for MACD via scale margins

      // Convert candlestick data to line chart data using closing prices
      const lineData = chartData.map((candle) => ({
        time: candle.time,
        value: candle.close,
      }));

      // Determine line color based on overall price trend
      const firstPrice = chartData.length > 0 ? chartData[0].close : 0;
      const lastPrice = chartData.length > 0 ? chartData[chartData.length - 1].close : 0;
      const isUpTrend = lastPrice >= firstPrice;
      const lineColor = isUpTrend
        ? tradingViewColors.bullishGreen
        : tradingViewColors.bearishRed;

      // Add line series for main price data with TradingView colors
      const lineSeries = chart.addSeries(LineSeries, {
        color: lineColor,
        lineWidth: 2,
        lineStyle: LineStyle.Solid,
        crosshairMarkerVisible: true,
        crosshairMarkerRadius: 4,
        crosshairMarkerBorderColor: lineColor,
        crosshairMarkerBackgroundColor: lineColor,
        priceScaleId: "right",
        priceFormat: {
          type: "price",
          precision: 2,
          minMove: 0.01,
        },
      });

      // Set the line chart data
      lineSeries.setData(lineData);

      // Fit the chart to show all data points properly
      if (chartData.length > 0) {
        // Set visible range to show all data with some padding
        const firstTime = chartData[0].time as number;
        const lastTime = chartData[chartData.length - 1].time as number;
        const timeRange = lastTime - firstTime;
        const padding = timeRange * 0.05; // 5% padding on each side

        chart.timeScale().setVisibleRange({
          from: (firstTime - padding) as UTCTimestamp,
          to: (lastTime + padding) as UTCTimestamp,
        });
      }

      // Add MACD indicator if enabled and data available
      if (chartOptions.showMACD && chartMacdData.length > 0) {
        // Calculate proper MACD with signal line
        const macdResult = calculateMACDFromPrices(chartData);

        if (macdResult.histogram.length > 0) {
          // Add MACD histogram first to create the price scale
          const macdHistogramSeries = chart.addSeries(HistogramSeries, {
            color: tradingViewColors.macdBlue,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });
          macdHistogramSeries.setData(macdResult.histogram);

          // Now configure the MACD price scale after the series is created
          chart.priceScale("macd").applyOptions({
            scaleMargins: {
              top: 0.7, // Start MACD at 70% down
              bottom: 0.05,
            },
            borderColor: tradingViewColors.borderColor,
          });

          // Add MACD line
          const macdLineSeries = chart.addSeries(LineSeries, {
            color: tradingViewColors.macdBlue,
            lineWidth: 2,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });
          macdLineSeries.setData(
            macdResult.macdLine.map((point) => ({
              time: point.time as UTCTimestamp,
              value: point.value,
            }))
          );

          // Add Signal line
          const signalLineSeries = chart.addSeries(LineSeries, {
            color: tradingViewColors.signalOrange,
            lineWidth: 2,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });
          signalLineSeries.setData(
            macdResult.signalLine.map((point) => ({
              time: point.time as UTCTimestamp,
              value: point.value,
            }))
          );

          // Add zero line for MACD
          const zeroLineSeries = chart.addSeries(LineSeries, {
            color: tradingViewColors.gridLines,
            lineWidth: 1,
            lineStyle: LineStyle.Dotted,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });

          // Create zero line data
          const zeroLineData = macdResult.macdLine.map((point) => ({
            time: point.time as UTCTimestamp,
            value: 0,
          }));
          zeroLineSeries.setData(zeroLineData);
        }
      }

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current && chartRef.current) {
          chartRef.current.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener("resize", handleResize);

      // Cleanup function
      return () => {
        window.removeEventListener("resize", handleResize);
        if (chartRef.current) {
          chartRef.current.remove();
          chartRef.current = null;
        }
      };
    }, [chartData, chartMacdData, width, chartHeight, chartOptions]);

    // Show loading state
    if (state.isLoading) {
      return (
        <div
          className={`w-full flex items-center justify-center ${className}`}
          style={{ height: chartHeight }}
        >
          <div className="text-muted-foreground">Loading chart data...</div>
        </div>
      );
    }

    // Show error state
    if (state.error) {
      return (
        <div
          className={`w-full flex items-center justify-center ${className}`}
          style={{ height: chartHeight }}
        >
          <div className="text-red-500">Error: {state.error}</div>
        </div>
      );
    }

    return (
      <div className={`w-full h-full ${className}`}>
        <div ref={chartContainerRef} className="w-full" style={{ height: chartHeight }} />
      </div>
    );
  }
);

Chart.displayName = "Chart";
