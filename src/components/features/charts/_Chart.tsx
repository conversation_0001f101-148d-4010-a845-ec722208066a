import React, { useEffect, useRef, memo, useMemo, useState, useCallback } from "react";
import {
  create<PERSON><PERSON>,
  ColorType,
  IChartApi,
  CandlestickData,
  HistogramData,
  CandlestickSeries,
  HistogramSeries,
  LineSeries,
  CrosshairMode,
  LineStyle,
  UTCTimestamp,
} from "lightweight-charts";
import { useChartState, ChartOptions } from "./stores";
import { calculateMACDFromPrices } from "./utils";
import { getChartData } from "@/data/apis";

// Time interval types
export type TimeInterval = "12h" | "1D" | "2D" | "3D" | "1W";

export interface TimeIntervalOption {
  value: TimeInterval;
  label: string;
  days: number;
}

interface ChartProps {
  data?: CandlestickData[];
  macdData?: HistogramData[];
  width?: number;
  height?: number;
  options?: Partial<ChartOptions>;
  onDataUpdate?: (data: CandlestickData[], macdData: HistogramData[]) => void;
  className?: string;
}

// Time interval configuration
const TIME_INTERVALS: TimeIntervalOption[] = [
  { value: "12h", label: "12h", days: 0.5 },
  { value: "1D", label: "1D", days: 1 },
  { value: "2D", label: "2D", days: 2 },
  { value: "3D", label: "3D", days: 3 },
  { value: "1W", label: "1W", days: 7 },
];

export const Chart = memo(
  ({
    data: propData,
    macdData: propMacdData,
    width,
    height,
    options: propOptions,
    onDataUpdate,
    className = "",
  }: ChartProps) => {
    const chartContainerRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<IChartApi | null>(null);

    // Time interval state
    const [selectedInterval, setSelectedInterval] = useState<TimeInterval>("1D");
    const [isLoadingInterval, setIsLoadingInterval] = useState(false);

    // Use internal state management if no external data provided
    const {
      state,
      options,
      updateOptions,
      updateData,
      generateMockData,
      fetchRealData,
      refreshData,
    } = useChartState(propData, propMacdData);

    // Use prop options if provided, otherwise use state options
    const chartOptions = useMemo(
      () => (propOptions ? { ...options, ...propOptions } : options),
      [options, propOptions]
    );
    const chartHeight = height || chartOptions.height;

    // Determine which data to use based on time interval functionality
    // If time intervals are being used (API data source), prioritize internal state
    // Otherwise, use external data if provided
    const shouldUseInternalState =
      chartOptions.dataSource === "api" &&
      chartOptions.symbol &&
      chartOptions.symbol !== "PORTFOLIO";
    const chartData = shouldUseInternalState ? state.data : propData || state.data;
    const chartMacdData = shouldUseInternalState
      ? state.macdData
      : propMacdData || state.macdData;

    // Handle time interval changes
    const handleIntervalChange = useCallback(
      async (interval: TimeInterval) => {
        if (interval === selectedInterval || isLoadingInterval) return;

        setIsLoadingInterval(true);
        setSelectedInterval(interval);

        try {
          // Find the days for the selected interval
          const intervalConfig = TIME_INTERVALS.find((i) => i.value === interval);
          const days = intervalConfig?.days || 1;

          // Fetch new data for the selected interval
          if (chartOptions.dataSource === "api" && chartOptions.symbol) {
            const result = await getChartData(
              chartOptions.symbol,
              Math.ceil(days),
              false
            );

            updateData(result.data, result.macdData);
          }
        } catch (error) {
          console.error("Failed to fetch data for interval:", interval, error);
        } finally {
          setIsLoadingInterval(false);
        }
      },
      [
        selectedInterval,
        isLoadingInterval,
        chartOptions.dataSource,
        chartOptions.symbol,
        updateData,
      ]
    );

    // Generate initial data if none provided
    useEffect(() => {
      if (!propData && !propMacdData && state.data.length === 0) {
        if (options.dataSource === "api") {
          fetchRealData();
        } else {
          generateMockData();
        }
      }
    }, [
      propData,
      propMacdData,
      state.data.length,
      options.dataSource,
      fetchRealData,
      generateMockData,
    ]);

    // Update options when prop options change
    useEffect(() => {
      if (propOptions) {
        updateOptions(propOptions);
      }
    }, [propOptions, updateOptions]);

    // Refresh data when data source or symbol changes
    useEffect(() => {
      if (!propData && !propMacdData && state.data.length > 0) {
        refreshData();
      }
    }, [
      options.dataSource,
      options.symbol,
      propData,
      propMacdData,
      state.data.length,
      refreshData,
    ]);

    // Notify parent of data updates
    useEffect(() => {
      if (onDataUpdate && chartData.length > 0 && chartMacdData.length > 0) {
        onDataUpdate(chartData, chartMacdData);
      }
    }, [chartData, chartMacdData, onDataUpdate]);

    useEffect(() => {
      if (!chartContainerRef.current || chartData.length === 0) return;

      // TradingView-style color scheme
      const tradingViewColors = {
        background: "#131722",
        gridLines: "#1e222d",
        textColor: "#d1d4dc",
        borderColor: "#2a2e39",
        bullishGreen: "#26a69a",
        bearishRed: "#ef5350",
        macdBlue: "#2196f3",
        signalOrange: "#ff9800",
      };

      // Create the chart with TradingView-style configuration
      const chart = createChart(chartContainerRef.current, {
        layout: {
          background: {
            type: ColorType.Solid,
            color: tradingViewColors.background,
          },
          textColor: tradingViewColors.textColor,
          fontSize: 12,
          fontFamily: "'Trebuchet MS', Arial, sans-serif",
        },
        grid: {
          vertLines: {
            color: tradingViewColors.gridLines,
            style: LineStyle.Dotted,
            visible: true,
          },
          horzLines: {
            color: tradingViewColors.gridLines,
            style: LineStyle.Dotted,
            visible: true,
          },
        },
        crosshair: {
          mode: CrosshairMode.Normal,
          vertLine: {
            color: tradingViewColors.textColor,
            width: 1,
            style: LineStyle.Dashed,
            labelBackgroundColor: tradingViewColors.background,
          },
          horzLine: {
            color: tradingViewColors.textColor,
            width: 1,
            style: LineStyle.Dashed,
            labelBackgroundColor: tradingViewColors.background,
          },
        },
        rightPriceScale: {
          borderColor: tradingViewColors.borderColor,
          textColor: tradingViewColors.textColor,
          scaleMargins: {
            top: 0.1,
            bottom: 0.1,
          },
        },
        timeScale: {
          borderColor: tradingViewColors.borderColor,
          timeVisible: true,
          secondsVisible: false,
          rightOffset: 12,
          barSpacing: 3,
          minBarSpacing: 0.5,
        },
        width: width || chartContainerRef.current.clientWidth,
        height: chartHeight,
      });

      chartRef.current = chart;

      // Chart will use 70% for main price chart and 30% for MACD via scale margins

      // Convert candlestick data to line chart data using closing prices
      const lineData = chartData.map((candle) => ({
        time: candle.time,
        value: candle.close,
      }));

      // Determine line color based on overall price trend
      const firstPrice = chartData.length > 0 ? chartData[0].close : 0;
      const lastPrice = chartData.length > 0 ? chartData[chartData.length - 1].close : 0;
      const isUpTrend = lastPrice >= firstPrice;
      const lineColor = isUpTrend
        ? tradingViewColors.bullishGreen
        : tradingViewColors.bearishRed;

      // Add line series for main price data with TradingView colors
      const lineSeries = chart.addSeries(LineSeries, {
        color: lineColor,
        lineWidth: 2,
        lineStyle: LineStyle.Solid,
        crosshairMarkerVisible: true,
        crosshairMarkerRadius: 4,
        crosshairMarkerBorderColor: lineColor,
        crosshairMarkerBackgroundColor: lineColor,
        priceScaleId: "right",
        priceFormat: {
          type: "price",
          precision: 2,
          minMove: 0.01,
        },
      });

      // Set the line chart data
      lineSeries.setData(lineData);

      // Fit the chart to show all data points properly
      if (chartData.length > 0) {
        // Set visible range to show all data with some padding
        const firstTime = chartData[0].time as number;
        const lastTime = chartData[chartData.length - 1].time as number;
        const timeRange = lastTime - firstTime;
        const padding = timeRange * 0.05; // 5% padding on each side

        chart.timeScale().setVisibleRange({
          from: (firstTime - padding) as UTCTimestamp,
          to: (lastTime + padding) as UTCTimestamp,
        });
      }

      // Add MACD indicator if enabled and data available
      if (chartOptions.showMACD && chartMacdData.length > 0) {
        // Calculate proper MACD with signal line
        const macdResult = calculateMACDFromPrices(chartData);

        if (macdResult.histogram.length > 0) {
          // Add MACD histogram first to create the price scale
          const macdHistogramSeries = chart.addSeries(HistogramSeries, {
            color: tradingViewColors.macdBlue,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });
          macdHistogramSeries.setData(macdResult.histogram);

          // Now configure the MACD price scale after the series is created
          chart.priceScale("macd").applyOptions({
            scaleMargins: {
              top: 0.7, // Start MACD at 70% down
              bottom: 0.05,
            },
            borderColor: tradingViewColors.borderColor,
          });

          // Add MACD line
          const macdLineSeries = chart.addSeries(LineSeries, {
            color: tradingViewColors.macdBlue,
            lineWidth: 2,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });
          macdLineSeries.setData(
            macdResult.macdLine.map((point) => ({
              time: point.time as UTCTimestamp,
              value: point.value,
            }))
          );

          // Add Signal line
          const signalLineSeries = chart.addSeries(LineSeries, {
            color: tradingViewColors.signalOrange,
            lineWidth: 2,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });
          signalLineSeries.setData(
            macdResult.signalLine.map((point) => ({
              time: point.time as UTCTimestamp,
              value: point.value,
            }))
          );

          // Add zero line for MACD
          const zeroLineSeries = chart.addSeries(LineSeries, {
            color: tradingViewColors.gridLines,
            lineWidth: 1,
            lineStyle: LineStyle.Dotted,
            priceFormat: {
              type: "volume",
              precision: 4,
            },
            priceScaleId: "macd",
          });

          // Create zero line data
          const zeroLineData = macdResult.macdLine.map((point) => ({
            time: point.time as UTCTimestamp,
            value: 0,
          }));
          zeroLineSeries.setData(zeroLineData);
        }
      }

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current && chartRef.current) {
          chartRef.current.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener("resize", handleResize);

      // Cleanup function
      return () => {
        window.removeEventListener("resize", handleResize);
        if (chartRef.current) {
          chartRef.current.remove();
          chartRef.current = null;
        }
      };
    }, [chartData, chartMacdData, width, chartHeight, chartOptions]);

    // Show loading state
    if (state.isLoading) {
      return (
        <div
          className={`w-full flex items-center justify-center ${className}`}
          style={{ height: chartHeight }}
        >
          <div className="text-muted-foreground">Loading chart data...</div>
        </div>
      );
    }

    // Show error state
    if (state.error) {
      return (
        <div
          className={`w-full flex items-center justify-center ${className}`}
          style={{ height: chartHeight }}
        >
          <div className="text-red-500">Error: {state.error}</div>
        </div>
      );
    }

    return (
      <div className={`w-full h-full ${className}`}>
        {/* Time Interval Selection Panel - Only show for API data sources */}
        {shouldUseInternalState && (
          <div className="flex items-center justify-start gap-1 mb-3 px-2">
            {TIME_INTERVALS.map((interval) => (
              <button
                key={interval.value}
                onClick={() => handleIntervalChange(interval.value)}
                disabled={isLoadingInterval}
                className={`
                px-3 py-1.5 text-xs font-medium rounded transition-all duration-200
                ${
                  selectedInterval === interval.value
                    ? "bg-blue-600 text-white shadow-sm"
                    : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white"
                }
                ${isLoadingInterval ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
                border border-gray-700 hover:border-gray-600
              `}
              >
                {interval.label}
              </button>
            ))}
            {isLoadingInterval && (
              <div className="ml-2 text-xs text-gray-400">Loading...</div>
            )}
          </div>
        )}

        {/* Chart Container */}
        <div ref={chartContainerRef} className="w-full" style={{ height: chartHeight }} />
      </div>
    );
  }
);

Chart.displayName = "Chart";
