-- Supabase Bitcoin Data Storage Schema
-- Run this in your Supabase SQL editor to create the required tables

-- Create bitcoin_price_data table
CREATE TABLE IF NOT EXISTS bitcoin_price_data (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL DEFAULT 'BITCOIN',
    time_unix BIGINT NOT NULL,
    open DECIMAL(15,8) NOT NULL,
    high DECIMAL(15,8) NOT NULL,
    low DECIMAL(15,8) NOT NULL,
    close DECIMAL(15,8) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bitcoin_macd_data table
CREATE TABLE IF NOT EXISTS bitcoin_macd_data (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL DEFAULT 'BITCOIN',
    time_unix BIGINT NOT NULL,
    macd_value DECIMAL(15,8) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_bitcoin_price_symbol_time ON bitcoin_price_data(symbol, time_unix);
CREATE INDEX IF NOT EXISTS idx_bitcoin_price_time ON bitcoin_price_data(time_unix);
CREATE INDEX IF NOT EXISTS idx_bitcoin_macd_symbol_time ON bitcoin_macd_data(symbol, time_unix);
CREATE INDEX IF NOT EXISTS idx_bitcoin_macd_time ON bitcoin_macd_data(time_unix);

-- Create unique constraints to prevent duplicate data
CREATE UNIQUE INDEX IF NOT EXISTS idx_bitcoin_price_unique ON bitcoin_price_data(symbol, time_unix);
CREATE UNIQUE INDEX IF NOT EXISTS idx_bitcoin_macd_unique ON bitcoin_macd_data(symbol, time_unix);

-- Create RPC functions for table creation (used by the application)
CREATE OR REPLACE FUNCTION create_bitcoin_price_table()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Table creation is handled above, this function exists for compatibility
    RAISE NOTICE 'Bitcoin price table already exists or created';
END;
$$;

CREATE OR REPLACE FUNCTION create_bitcoin_macd_table()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Table creation is handled above, this function exists for compatibility
    RAISE NOTICE 'Bitcoin MACD table already exists or created';
END;
$$;

-- Create function to get data statistics
CREATE OR REPLACE FUNCTION get_bitcoin_data_stats()
RETURNS TABLE(
    price_count BIGINT,
    macd_count BIGINT,
    latest_price_time BIGINT,
    latest_macd_time BIGINT,
    data_age_hours NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM bitcoin_price_data WHERE symbol = 'BITCOIN') as price_count,
        (SELECT COUNT(*) FROM bitcoin_macd_data WHERE symbol = 'BITCOIN') as macd_count,
        (SELECT MAX(time_unix) FROM bitcoin_price_data WHERE symbol = 'BITCOIN') as latest_price_time,
        (SELECT MAX(time_unix) FROM bitcoin_macd_data WHERE symbol = 'BITCOIN') as latest_macd_time,
        EXTRACT(EPOCH FROM (NOW() - (SELECT MAX(created_at) FROM bitcoin_price_data WHERE symbol = 'BITCOIN'))) / 3600 as data_age_hours;
END;
$$;

-- Create function to clean old data (optional, for maintenance)
CREATE OR REPLACE FUNCTION clean_old_bitcoin_data(days_to_keep INTEGER DEFAULT 365)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    cutoff_time BIGINT;
    deleted_count INTEGER;
BEGIN
    cutoff_time := EXTRACT(EPOCH FROM NOW() - INTERVAL '1 day' * days_to_keep);
    
    -- Delete old price data
    DELETE FROM bitcoin_price_data 
    WHERE symbol = 'BITCOIN' AND time_unix < cutoff_time;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Delete old MACD data
    DELETE FROM bitcoin_macd_data 
    WHERE symbol = 'BITCOIN' AND time_unix < cutoff_time;
    
    RETURN deleted_count;
END;
$$;

-- Enable Row Level Security (RLS) for better security
ALTER TABLE bitcoin_price_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE bitcoin_macd_data ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access (adjust as needed for your security requirements)
CREATE POLICY "Allow public read access to bitcoin price data" ON bitcoin_price_data
    FOR SELECT USING (true);

CREATE POLICY "Allow public read access to bitcoin macd data" ON bitcoin_macd_data
    FOR SELECT USING (true);

-- Create policies for authenticated insert/update (adjust as needed)
CREATE POLICY "Allow authenticated insert to bitcoin price data" ON bitcoin_price_data
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' OR auth.role() = 'anon');

CREATE POLICY "Allow authenticated insert to bitcoin macd data" ON bitcoin_macd_data
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' OR auth.role() = 'anon');

CREATE POLICY "Allow authenticated delete from bitcoin price data" ON bitcoin_price_data
    FOR DELETE USING (auth.role() = 'authenticated' OR auth.role() = 'anon');

CREATE POLICY "Allow authenticated delete from bitcoin macd data" ON bitcoin_macd_data
    FOR DELETE USING (auth.role() = 'authenticated' OR auth.role() = 'anon');

-- Create a view for easy data access with time formatting
CREATE OR REPLACE VIEW bitcoin_data_summary AS
SELECT 
    p.symbol,
    p.time_unix,
    TO_TIMESTAMP(p.time_unix) as time_formatted,
    p.open,
    p.high,
    p.low,
    p.close,
    m.macd_value,
    p.created_at as price_created_at,
    m.created_at as macd_created_at
FROM bitcoin_price_data p
LEFT JOIN bitcoin_macd_data m ON p.symbol = m.symbol AND p.time_unix = m.time_unix
WHERE p.symbol = 'BITCOIN'
ORDER BY p.time_unix DESC;

-- Grant necessary permissions
GRANT SELECT ON bitcoin_data_summary TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_bitcoin_data_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION clean_old_bitcoin_data(INTEGER) TO authenticated;

-- Insert a comment for documentation
COMMENT ON TABLE bitcoin_price_data IS 'Stores Bitcoin OHLC price data for time interval analysis';
COMMENT ON TABLE bitcoin_macd_data IS 'Stores Bitcoin MACD indicator data for technical analysis';
COMMENT ON FUNCTION get_bitcoin_data_stats() IS 'Returns statistics about stored Bitcoin data';
COMMENT ON FUNCTION clean_old_bitcoin_data(INTEGER) IS 'Removes Bitcoin data older than specified days';
COMMENT ON VIEW bitcoin_data_summary IS 'Combined view of Bitcoin price and MACD data with formatted timestamps';
