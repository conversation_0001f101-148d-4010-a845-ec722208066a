## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase (Database & Authentication)

## Environment Setup

This project requires environment variables to be configured before running. Follow these steps:

### 1. Copy the environment template

```sh
cp .env.example .env
```

### 2. Configure project structure

```
src/components/features
├── charts/
│   ├── _Chart.tsx(Parent component use underscore in head)
│   ├── ChartModal.tsx ( Children component is below of parent)
│   ├── utils.ts
│   └── stores.ts
```

features
├── sample-a
│ ├── details
│ │ ├── apis.ts
│ │ ├── components
│ │ │ ├── SampleADetailsContent.tsx
│ │ │ └── SampleADetailsDialog.tsx
│ │ ├── funcs.ts
│ │ ├── stores.ts
│ │ └── types.ts
│ └── list
│ ├── apis.ts
│ ├── components
│ │ ├── SampleAItem.tsx
│ │ └── SampleAList.tsx
│ ├── funcs.ts
│ ├── stores.ts
│ └── types.ts
└── sample-b
├── details
│ ├── apis.ts
│ ├── components
│ │ ├── SampleBDetailsContent.tsx
│ │ └── SampleBDetailsDialog.tsx
│ ├── funcs.ts
│ ├── stores.ts
│ └── types.ts
└── list
├── apis.ts
├── components
│ ├── SampleBItem.tsx
│ └── SampleBList.tsx
├── funcs.ts
├── stores.ts
└── types.ts
